# InvestTask

与 investFlow 一起，配套相关的自动化任务

## 安装

1. 克隆仓库

```bash
git clone https://github.com/yourusername/investTask.git
cd investTask
```

2. 安装依赖

```bash
poetry install
```

## 配置

### 数据库配置

编辑 `config.py` 文件，配置 MongoDB 连接信息。

### Supervisor 配置

项目使用 Supervisor 管理长期运行的任务。配置文件位于 `supervisor/` 目录下。

### poetry 初始化
`poetry env use python3.12`
`poetry install`

### 依赖冲突问题
`poetry run pip install akshare`

## 启动服务

### 使用 Supervisor 启动任务

```bash
# 进入项目目录
cd /path/to/investTask

# 启动 supervisor
supervisord -c supervisor/supervisord.conf

# 查看任务状态
supervisorctl -c supervisor/supervisord.conf status

# 启动特定任务
supervisorctl -c supervisor/supervisord.conf start stock_sector_rank

# 停止特定任务
supervisorctl -c supervisor/supervisord.conf stop stock_sector_rank

# 重启特定任务
supervisorctl -c supervisor/supervisord.conf restart stock_sector_rank

# 重新加载配置
supervisorctl -c supervisor/supervisord.conf reread

# 更新配置（会自动启动新任务）
supervisorctl -c supervisor/supervisord.conf update

# 停止所有任务
supervisorctl -c supervisor/supervisord.conf stop all

# 重启所有任务
supervisorctl -c supervisor/supervisord.conf restart all

# 关闭 supervisor
supervisorctl -c supervisor/supervisord.conf shutdown

# 启动所有股票相关任务
supervisorctl -c supervisor/supervisord.conf start "stock:*"

# 启动所有加密货币相关任务
supervisorctl -c supervisor/supervisord.conf start "crypto:*"

# 启动所有任务
supervisorctl -c supervisor/supervisord.conf start "all:*"

# 停止特定组
supervisorctl -c supervisor/supervisord.conf stop "stock:*"
```

### Supervisor Web 界面

Supervisor 提供了一个 Web 界面用于监控和管理任务。

- 访问地址: http://localhost:9001
- 用户名: yc123h
- 密码: 在配置文件中设置

## 项目结构

```
investTask/
├── database/       # 数据库模型和连接
├── logs/           # 日志文件
│   └── supervisor/ # Supervisor 日志
├── supervisor/     # Supervisor 配置
│   └── conf.d/     # 任务配置
├── tasks/          # 任务脚本
└── utils/          # 工具函数
```

## 主要任务

### 股票板块排名 (stock_sector_rank)

每个交易日 15:20 自动运行，获取最新的股票板块排名数据，分析变化并发送通知。

## 日志

日志文件保存在 `logs/` 目录下：
- 应用日志: `logs/app_*.log`
- Supervisor 日志: `logs/supervisor/*.log`

## 贡献

欢迎提交 Issue 和 Pull Request。

## 许可

[MIT License](LICENSE)