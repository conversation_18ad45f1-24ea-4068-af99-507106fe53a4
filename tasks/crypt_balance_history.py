from datetime import datetime, timezone
import json
import os
import sys

from mongoengine import NotUniqueError
import requests

from utils.redis_client import RedisSentinelClient


# 将项目根目录添加到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from database.crypto_earn_rewards_history import CryptoEarnRewardsHistory
from database.crypto_account_history import AccountHistory
from utils.common import now
from utils.notification import send_message
from database.db import connect_db
from database.exchange_keys import ExchangeKeys
from utils.cipher import decrypt_data
from utils.exchange import fetch_positions, get_position, get_total_asset
from utils.constant import exchange_proxy, get_total_asset_key
from utils import init_logger
from redis import Redis
import ccxt
from apscheduler.schedulers.blocking import BlockingScheduler

redis = RedisSentinelClient.getInstance()


class CryptoBalanceHistory:
    """
    加密货币账户资金历史记录机器人
    """

    def __init__(self, exchange_key: ExchangeKeys):
        self.exchange_key = exchange_key
        self.logger = init_logger(
            log_file=f"logs/crypto_balance_history/{self.exchange_key.exchange}_{self.exchange_key.account}_{{time:YYYY-MM-DD}}.log",
            level="INFO",
            console_level="INFO",
            prefix=f"crypto_balance_history:{self.exchange_key.account}",
        )

        self.logger.info("初始化加密货币余额历史记录机器人")
        self._set_exchange()
        self.total_asset_key = get_total_asset_key(self.exchange_key.account)

    def _set_exchange(self):
        config = {
            "apiKey": self.exchange_key.api_key,
            "secret": decrypt_data(self.exchange_key.api_secret),
            "enableRateLimit": True,
            "proxies": {
                "http": exchange_proxy,
                "https": exchange_proxy,
            },
        }

        self.exchange = ccxt.binance({**config, "options": {"defaultType": "future"}})

    def set_asset_and_positions(self):
        self.logger.info(
            f"开始获取账户资产和持仓信息: {self.exchange_key.exchange}_{self.exchange_key.account}"
        )
        try:
            positions = fetch_positions(self.exchange)
            position_list = []
            asset = get_total_asset(self.exchange)
            for position in positions:
                position_list.append(
                    {
                        "symbol": position["symbol"],
                        "side": position["side"],
                        "position_amount": position["contracts"],
                        "position_price": position["entryPrice"],
                        "unrealized_profit": position["unrealizedPnl"],
                        "liquidation_price": position["liquidationPrice"],
                        "mark_price": position["markPrice"],
                        "leverage_rate": position["contracts"]
                        * position["entryPrice"]
                        / asset["total_asset"],
                        "margin": position["initialMargin"],
                        "margin_rate": position["marginRatio"],
                    }
                )
            asset_data = {
                "account": self.exchange_key.account,
                "exchange": self.exchange_key.exchange,
                "wallet_balance": asset["balance"]["info"]["totalCrossWalletBalance"],
                "unrealized_pnl": asset["balance"]["info"]["totalCrossUnPnl"],
                "total_asset": asset["total_asset"],
                "earn_flexible_asset": asset["usdt_earn_amount"],
                "positions": position_list,
                "updated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            }

            redis.set(self.total_asset_key, json.dumps(asset_data))
            self.logger.info(
                f"账户资产信息已更新: 总资产={asset['total_asset']}, 理财资产={asset['usdt_earn_amount']}, 持仓数量={len(position_list)}"
            )
        except Exception as e:
            send_message(f"获取账户资产和持仓信息失败: {str(e)}")
            self.logger.error(f"获取账户资产和持仓信息失败: {str(e)}")
            raise

    def save_db(self):
        self.logger.info(
            f"开始保存账户历史数据到数据库: {self.exchange_key.exchange}_{self.exchange_key.account}"
        )
        try:
            connect_db()
            asset = json.loads(redis.get(self.total_asset_key))
            asset["created_at"] = now()
            save_data = AccountHistory(**asset)
            save_data.save()
            self.logger.info(
                f"账户历史数据保存成功: 总资产={asset['total_asset']}, 时间={asset['created_at']}"
            )
        except Exception as e:
            send_message(f"保存账户历史数据失败: {str(e)}")
            self.logger.error(f"保存账户历史数据失败: {str(e)}")
            raise

    def set_earn_rewards_history(self):
        self.logger.info(
            f"开始获取 {self.exchange_key.exchange}_{self.exchange_key.account} 的收益记录"
        )
        try:
            rewards = self.exchange.sapi_get_simple_earn_flexible_history_rewardsrecord(
                {"type": "ALL", "asset": "USDT", "size": 5}
            )
            self.logger.info(f"获取到 {len(rewards['rows'])} 条收益记录")

            for reward in rewards["rows"]:
                try:
                    # 使用update_one和upsert=True来避免竞态条件
                    created_at = datetime.fromtimestamp(
                        int(reward["time"]) / 1000, tz=timezone.utc
                    )
                    self.logger.debug(
                        f"处理收益记录: {reward['productId']} - {reward['asset']} - {reward['rewards']} - {reward['type']} - {created_at.isoformat()}"
                    )

                    result = CryptoEarnRewardsHistory.objects(
                        account=self.exchange_key.account,
                        exchange=self.exchange_key.exchange,
                        asset=reward["asset"],
                        product_id=reward["productId"],
                        type=reward["type"],
                        created_at=created_at,
                    ).update_one(set__rewards=reward["rewards"], upsert=True)

                    if result == 0:  # 更新了0条记录，说明是新插入的
                        self.logger.info(
                            f"新增收益记录: {reward['productId']} - {reward['asset']} - {reward['rewards']} - {reward['type']}"
                        )
                    else:  # 更新了现有记录
                        self.logger.info(
                            f"更新收益记录: {reward['productId']} - {reward['asset']} - {reward['rewards']} - {reward['type']}"
                        )
                except Exception as e:
                    self.logger.error(
                        f"处理收益记录失败: {reward['productId']} - {reward['asset']} - {reward['rewards']} - {reward['type']}, 错误: {str(e)}"
                    )
                    raise

            self.logger.info(
                f"完成 {self.exchange_key.exchange}_{self.exchange_key.account} 的收益记录处理"
            )
        except Exception as e:
            send_message(f"获取收益记录失败: {str(e)}")
            self.logger.error(f"获取收益记录失败: {str(e)}")
            raise


def set_earn_rewards_data():
    """
    设置用于前端展示的理财收益数据
    """
    try:
        connect_db()
        pipeline = [
            {"$sort": {"created_at": 1}},
            {
                "$group": {
                    "_id": {
                        "date": {"$dateTrunc": {"date": "$created_at", "unit": "day"}},
                        "account": "$account",
                    },
                    "total_rewards": {"$sum": "$rewards"},
                }
            },
            {
                "$group": {
                    "_id": "$_id.date",
                    "rewards": {
                        "$push": {"account": "$_id.account", "reward": "$total_rewards"}
                    },
                    "total_rewards": {"$sum": "$total_rewards"},
                }
            },
            {"$sort": {"_id": 1}},
        ]
        db_data = CryptoEarnRewardsHistory.objects.aggregate(pipeline).to_list()
        exchange_key = ExchangeKeys.objects.filter(is_debug=False).all()
        redis_data = {
            "date": [],
            "account": {},
            "total": [],
        }
        for exchange in exchange_key:
            redis_data["account"][exchange.account] = []

        # 首先添加所有日期
        for data in db_data:
            redis_data["date"].append(data["_id"].strftime("%Y-%m-%d"))

        # 确保每个账户对应每个日期都有数据
        for account in redis_data["account"]:
            # 初始化每个账户的列表，长度与日期列表相同，默认值为0
            redis_data["account"][account] = [0] * len(redis_data["date"])

        # 填充实际数据
        for i, data in enumerate(db_data):
            for reward in data["rewards"]:
                if reward["account"] in redis_data["account"]:
                    redis_data["account"][reward["account"]][i] = reward["reward"]
            redis_data["total"].append(data["total_rewards"])

        redis.set("investTask:earnRewardsData", json.dumps(redis_data))
        print(f"理财收益数据设置成功: {len(db_data)} 条")
    except Exception as e:
        send_message(f"设置用于前端展示的理财收益数据失败: {str(e)}")
        print(f"设置用于前端展示的理财收益数据失败: {str(e)}")
        raise


def set_exchange_rate():
    """
    获取当前 USD 到 CNY 的汇率。

    :param api_key: 聚合数据 API 的密钥
    :return: 汇率数值（float），若失败则抛出异常或返回 None
    """
    print("开始获取 USD 到 CNY 的汇率")
    url = "http://op.juhe.cn/onebox/exchange/currency"
    params = {
        "key": "c992387965e4afe68d9799445a19cfa3",
        "from": "USD",
        "to": "CNY",
        "version": "2",
    }

    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        data = response.json()

        if data["error_code"] == 0:
            for item in data.get("result", []):
                if item.get("currencyF") == "USD":
                    redis.set("investTask:exchangeRate", json.dumps(item))
                    print(f"汇率获取成功: USD/CNY = {item.get('result', 'N/A')}")
                    return
            print("未找到 USD 到 CNY 的汇率数据")
            raise ValueError("未找到 USD 到 CNY 的汇率数据")
        else:
            print(f"API 请求失败：{data['reason']} (错误码: {data['error_code']})")
            raise ValueError(
                f"API 请求失败：{data['reason']} (错误码: {data['error_code']})"
            )

    except requests.RequestException as e:
        print(f"请求汇率接口失败：{e}")
        send_message(f"请求汇率接口失败：{e}")
        raise ConnectionError(f"请求汇率接口失败：{e}")


def run():
    print("开始初始化加密货币余额历史记录调度器")
    scheduler = BlockingScheduler()
    scheduler.add_job(set_exchange_rate, "interval", hours=1)
    print("已添加汇率获取任务: 每小时执行一次")

    scheduler.add_job(set_earn_rewards_data, "cron", hour="8,9", minute=5)
    print("已添加理财数据展示任务: 每天 8点、9 点执行")
    connect_db()
    exchange_keys = [key for key in ExchangeKeys.objects().all() if not key.is_debug]
    print(f"找到 {len(exchange_keys)} 个非调试交易所账户")

    for exchange_key in exchange_keys:
        robot = CryptoBalanceHistory(exchange_key)
        robot.set_asset_and_positions()
        scheduler.add_job(robot.set_earn_rewards_history, "cron", hour="8,9")
        scheduler.add_job(robot.set_asset_and_positions, "interval", minutes=1)
        # 在每天的0点、8点和16点执行保存数据库的任务
        scheduler.add_job(robot.save_db, "cron", hour="0,8,16")
        print(f"已为账户 {exchange_key.exchange}_{exchange_key.account} 添加定时任务")

    print("所有任务已添加，开始运行调度器")
    scheduler.start()


if __name__ == "__main__":
    run()
