import json
import os
import sys
import time
from datetime import datetime
from typing import Any, Dict, List

from utils.constant import exchange_proxy, get_orders_key
from utils.redis_client import RedisSentinelClient

# 将项目根目录添加到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

import ccxt
import redis
from apscheduler.schedulers.blocking import BlockingScheduler

import config
from database.crypto_grid_strategy import CryptoGridStrategy
from database.db import connect_db
from service.gird_strategy import get_grid_data
from utils import init_logger
from utils.cipher import decrypt_data
from utils.common import now, to_fixed
from utils.exchange import (
    cancel_all_orders,
    create_order,
    get_order,
    get_position,
    get_ticker,
    get_total_asset,
)
from utils.notification import send_message
from utils.shutdown_manager import ShutdownManager


class CryptoGridRobot:
    def __init__(self, grid_data: CryptoGridStrategy):
        self.exchange_key = grid_data.exchange_key
        self.grid_data = grid_data
        self.redis = RedisSentinelClient.getInstance()
        self.symbol = grid_data.symbol
        self.orders: List[Dict[str, Any]] = []
        self.leverage = 75
        self._init()
        self.logger.info(
            f"初始化加密货币网格机器人: {self.exchange_key.account}, 交易对: {self.symbol}"
        )

    def init_logger(self):
        # 使用loguru的内置日期格式，支持自动按日期切换
        self.logger = init_logger(
            log_file=f"logs/crypto_grid/{self.exchange_key.account}{self.grid_data.is_debug and '(debug)' or ''}/{self.symbol.replace('/', '_')}/{{time:YYYY-MM-DD}}.log",
            level="INFO",
            console_level="INFO",
            prefix=f"{self.exchange_key.account}{self.grid_data.is_debug and '(debug)' or ''}_{self.symbol}",
        )

    def _set_redis_key(self):
        account = (
            f"{self.exchange_key.account}{self.grid_data.is_debug and '(debug)' or ''}"
        )
        symbol = self.symbol.replace(":", "_")
        self.orders_key = get_orders_key(account, symbol)
        self.lock_key = f"investTask:crypto:{account}:{symbol}:gridStrategy:lock"

    def _init(self):
        """
        初始化
        """
        self.init_logger()
        self.exchange = self._get_exchange()
        self._set_redis_key()
        self._clear_orders()
        self._update_grid_value()
        self._init_position()

    def _clear_orders(self):
        self.logger.debug(f"清除所有订单: {self.symbol}")
        cancel_all_orders(self.exchange, self.symbol)
        self.redis.delete(self.orders_key)
        self.orders.clear()

    def _get_orders(self):
        if self.orders:
            return self.orders

        orders = self.redis.get(self.orders_key)
        if orders:
            self.orders = json.loads(orders)
        else:
            self.orders = []
        return self.orders

    def _get_exchange(self):
        if self.exchange_key is None:
            return None
        exchange = ccxt.binance(
            {
                "apiKey": self.exchange_key.api_key,
                "secret": decrypt_data(self.exchange_key.api_secret),
                "enableRateLimit": True,
                "proxies": {
                    "http": exchange_proxy,
                    "https": exchange_proxy,
                },
                "options": {
                    "defaultType": "future",  # 使用合约交易
                },
            }
        )
        if self.exchange_key.is_debug:
            self.logger.info("交易所设置为调试模式")
            exchange.set_sandbox_mode(True)
        exchange.set_leverage(self.leverage, self.grid_data.symbol)
        self.logger.debug(f"设置杠杆倍数: {self.leverage}")
        return exchange

    def _get_position_amount(self):
        position, positions = get_position(self.exchange, self.symbol)
        return position[0]["contracts"] if position else 0

    def _init_position(self):
        """
        初始化持仓，空仓时执行
        """
        # 通过迭代器获取持仓信息，取数组第一个元素
        position = next(iter(get_position(self.exchange, self.symbol)[0]), {})

        if position and not self.grid_data.target_price:
            self.grid_data.target_price = to_fixed(
                position["entryPrice"] * (1 + self.grid_data.grid_size), 2
            )
            self.logger.info(f"初始化目标价格: {self.grid_data.target_price}")

        position_amount = position.get("contracts", 0)
        if position_amount > 0:
            self.logger.info(f"已有持仓: {position_amount}, 跳过初始化")
            return

        self.logger.info(f"开始初始化持仓: {self.symbol}")
        ticker = get_ticker(self.exchange, self.symbol)
        last = ticker["last"]
        min_position = (
            self.grid_data.grid_value / self.grid_data.grid_size
            - self.grid_data.grid_value
        ) / last - (self.grid_data.grid_value / self.grid_data.grid_size) / (
            last * (self.grid_data.grid_size + 1)
        )
        max_position = (
            self.grid_data.grid_value / self.grid_data.grid_size
            + self.grid_data.grid_value
        ) / last - (self.grid_data.grid_value / self.grid_data.grid_size) / (
            last * (self.grid_data.grid_size + 1)
        )
        target_position = to_fixed(
            (min_position + max_position) / 2, self.grid_data.amount_precision
        )
        self.logger.info(f"创建初始买入订单, 数量: {target_position}, 当前价格: {last}")
        order = create_order(self.exchange, self.symbol, "buy", target_position)
        order_status = get_order(self.exchange, self.symbol, order["id"])
        while order_status["status"] != "closed":
            order_status = get_order(self.exchange, self.symbol, order["id"])
            time.sleep(1)
        self.logger.info(
            f"初始买入订单已完成, 成交价格: {order_status['price']}, 买入数量: {order_status['amount']}"
        )
        self.grid_data.target_price = to_fixed(
            order_status["price"] * (1 + self.grid_data.grid_size),
            self.grid_data.price_precision,
        )

    def _create_grid_order(self):
        if self._get_orders():
            return
        grid_data = get_grid_data(
            invest_asset=self.grid_data.invest_asset,
            leverage=self.grid_data.leverage,
            grid_size=self.grid_data.grid_size,
            target_price=self.grid_data.target_price,
            position_amount=self._get_position_amount(),
        )
        self.grid_data.buy_price = to_fixed(
            grid_data["buy_price"], self.grid_data.price_precision
        )
        self.grid_data.sell_price = to_fixed(
            grid_data["sell_price"], self.grid_data.price_precision
        )
        self.grid_data.buy_amount = to_fixed(
            grid_data["buy_amount"], self.grid_data.amount_precision
        )
        self.grid_data.sell_amount = to_fixed(
            grid_data["sell_amount"], self.grid_data.amount_precision
        )
        self.grid_data.grid_value = to_fixed(
            grid_data["trade_value"], self.grid_data.price_precision
        )

        self.logger.info(
            f"创建买入网格订单: 价格={self.grid_data.buy_price}, 数量={self.grid_data.buy_amount}"
        )
        buy_order = create_order(
            self.exchange,
            self.symbol,
            "buy",
            self.grid_data.buy_amount,
            self.grid_data.buy_price,
        )
        self.orders.append(buy_order)

        if self._get_position_amount() > self.grid_data.sell_amount * 2:
            self.logger.info(
                f"创建卖出网格订单: 价格={self.grid_data.sell_price}, 数量={self.grid_data.sell_amount}"
            )
            sell_order = create_order(
                self.exchange,
                self.symbol,
                "sell",
                self.grid_data.sell_amount,
                self.grid_data.sell_price,
            )
            self.orders.append(sell_order)

        self.redis.set(self.orders_key, json.dumps(self.orders))
        self.logger.debug(f"网格订单已创建并保存到Redis")

    def _check_order_status(self):
        for order in self._get_orders():
            order_status = get_order(self.exchange, self.symbol, order["id"])
            if order_status["status"] == "closed":
                self.logger.info(
                    f"订单已成交: ID={order['id']}, 方向={order.get('side')}, 价格={order_status.get('price')}, 数量={order_status.get('amount')}"
                )
                self._clear_orders()
                send_message(
                    f"【任务成功】加密货币网格机器人\n账号信息: {self.exchange_key.exchange}_{self.exchange_key.account}（{self.grid_data.is_debug and 'debug' or '正式'}）\n执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n成交价格: {order_status['price']}, 成交数量: {order_status['amount']}，成交方向: {order.get('side')}"
                )
                break
            if order_status["status"] == "canceled":
                self.logger.info(
                    f"订单已取消: ID={order['id']}, 方向={order.get('side')}, 价格={order_status.get('price')}, 数量={order_status.get('amount')}"
                )
                self._clear_orders()
                break

    def _update_grid_value(self, force=False):
        """
        更新单个网格价值以及更新投入资产
        :param force: 是否强制更新
        """
        self.grid_data.grid_value = to_fixed(
            self.grid_data.invest_asset
            * self.grid_data.leverage
            * self.grid_data.grid_size,
            2,
        )
        position_amount = self._get_position_amount()
        if position_amount > 0 and not force and self.grid_data.invest_asset:
            return
        min_asset = 10 / self.grid_data.leverage / self.grid_data.grid_size
        total_asset = get_total_asset(self.exchange, is_debug=self.grid_data.is_debug)
        available_usdt = to_fixed(
            total_asset["total_asset"] * self.grid_data.invest_ratio, 2
        )
        if available_usdt < min_asset:
            self.logger.warning(
                f"可用资产不足: {available_usdt} < {min_asset}, 停止机器人"
            )
            self.stop()
            sys.exit(0)
        if self.grid_data.invest_asset != available_usdt:
            self.logger.info(
                f"更新投入资产: {self.grid_data.invest_asset} -> {available_usdt}"
            )
            send_message(
                f"{self.exchange_key.account}{self.grid_data.is_debug and '(debug)' or ''} {self.symbol} 更新投入资产: {self.grid_data.invest_asset} -> {available_usdt}",
                "加密货币网格机器人",
            )
            self.grid_data.invest_asset = available_usdt
            self.grid_data.grid_value = to_fixed(
                self.grid_data.invest_asset
                * self.grid_data.leverage
                * self.grid_data.grid_size,
                2,
            )

    def _check_last_position(self):
        # 检查是否有平多（卖出）订单
        if not any(order.get("side") == "sell" for order in self._get_orders()) and any(
            order.get("side") == "buy" for order in self._get_orders()
        ):
            ticker = get_ticker(self.exchange, self.symbol)
            if ticker["last"] >= self.grid_data.sell_price:
                self.logger.info(
                    f"价格已达到卖出价格: 当前价格={ticker['last']}, 卖出价格={self.grid_data.sell_price}"
                )
                self._update_grid_value(force=True)
                self.grid_data.target_price = to_fixed(
                    ticker["last"] * (self.grid_data.grid_size + 1),
                    self.grid_data.price_precision,
                )
                self._clear_orders()
            return

    def _update_db(self):
        # 使用MongoEngine内置的_get_changed_fields方法检查变化
        if self.grid_data._get_changed_fields():
            self.logger.debug(
                f"网格策略数据已变更，保存到数据库: {self.grid_data.symbol}, 变更字段: {self.grid_data._get_changed_fields()}"
            )
            self.grid_data.updated_at = now()
            self.grid_data.save()

    def run(self):
        # 尝试获取锁，设置超时时间为10秒
        lock = self.redis.lock(self.lock_key, timeout=10)
        """
        当调用 lock.acquire(blocking=False) 时，线程会尝试立即获取锁。
        如果锁当前未被其他线程占用，则线程会成功获取锁，acquire 方法返回 True。
        如果锁当前已被其他线程占用，则线程不会等待，acquire 方法会立即返回 False，而不是阻塞当前线程。
        """
        acquire_result = lock.acquire(blocking=False)

        if not acquire_result:
            self.logger.debug(
                f"无法获取锁，可能有其他进程正在处理该交易对: {self.symbol}"
            )
            return

        try:
            self._create_grid_order()
            self._check_order_status()
            self._check_last_position()
            self._update_db()
            time.sleep(1)
        except Exception as e:
            self.logger.error(f"加密货币网格机器人运行失败: {e}")
            send_message(
                f"【任务失败】加密货币网格机器人\n账号信息: {self.grid_data.exchange_key.account}（{self.grid_data.is_debug and 'debug' or '正式'}）\n执行时间: {now()}\n错误信息: {str(e)}",
                "加密货币网格机器人",
            )
            raise e
        finally:
            # 确保锁被释放
            lock.release()

    def stop(self):
        self._clear_orders()
        self.logger.info(
            f"停止加密货币网格机器人: {self.grid_data.exchange_key.account}"
        )


class StrategyController:
    """
    策略控制器
    多进程管理加密货币网格机器人
    """

    def __init__(self, is_debug=False):
        self.strategy_list = []
        self.grid_robot_list = []
        self.is_debug = is_debug

    def generate_robot_list(self):
        """
        获取所有执行的加密货币网格执行策略
        """
        try:
            connect_db()
            self.strategy_list = CryptoGridStrategy.objects().all()
            print(f"从数据库获取到 {len(self.strategy_list)} 个策略")

            for strategy in self.strategy_list:
                self.grid_robot_list.append(CryptoGridRobot(strategy))

            print(f"成功创建 {len(self.grid_robot_list)} 个机器人")
        except Exception as e:
            print(f"获取策略列表失败: {e}")
            raise

    def stop_all(self):
        """
        停止所有机器人
        """
        print("正在停止所有加密货币网格机器人...")

        for robot in self.grid_robot_list:
            try:
                robot.stop()
            except Exception as e:
                print(f"停止机器人失败: {e}")

        print("所有加密货币网格机器人已停止")


def run():
    controller = None
    scheduler = None

    try:
        # 创建控制器
        controller = StrategyController()

        # 创建优雅关闭管理器
        shutdown_manager = ShutdownManager(max_shutdown_time=30)

        # 定义调度器关闭函数
        def shutdown_scheduler():
            print("正在关闭调度器...")
            if scheduler and scheduler.running:
                scheduler.shutdown(wait=True)
                print("调度器已关闭")
            controller.stop_all()

        # 注册关闭处理函数
        shutdown_manager.register_handler(shutdown_scheduler)

        # 初始化机器人
        print("正在初始化加密货币网格交易系统...")
        controller.generate_robot_list()

        scheduler = BlockingScheduler()

        # 添加任务
        for robot in controller.grid_robot_list:
            scheduler.add_job(
                robot.run,
                "interval",
                seconds=2,
                misfire_grace_time=5,
                max_instances=5,
                coalesce=True,
            )

        print("加密货币网格交易系统已启动，按 Ctrl+C 停止")

        scheduler.start()

        # 主循环，等待关闭信号
        while not shutdown_manager.is_shutdown_requested():
            # 检查关闭超时
            if shutdown_manager.check_shutdown_timeout():
                print("关闭超时，强制退出")
                sys.exit(1)  # 强制退出

            # 睡眠一小段时间，避免CPU占用过高
            time.sleep(0.1)

        print("收到关闭信号，等待系统完成关闭...")

    except Exception as e:
        print(f"程序运行过程中发生错误: {e}")
        send_message(
            f"【任务失败】加密货币网格交易系统\n执行时间: {now()}\n错误信息: {e}"
        )
        # 确保在发生异常时也能停止所有机器人
        if controller:
            controller.stop_all()
        if scheduler and scheduler.running:
            scheduler.shutdown(wait=False)
        sys.exit(1)


if __name__ == "__main__":
    run()
