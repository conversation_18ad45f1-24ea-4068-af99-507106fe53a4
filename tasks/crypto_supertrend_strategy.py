import json
import os
import sys
import time

import retry
from pymongo.errors import CollectionInvalid

from database.crypto_supertrend import CryptoSupertrend
from utils.common import (
    convert_to_binance_symbol,
    convert_to_ccxt_symbol,
    get_ip,
    get_precision,
    to_fixed,
)
from utils.notification import send_message
from utils.redis_client import RedisSentinelClient

# 将项目根目录添加到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime, timedelta
from typing import Any, Dict, List, TypedDict

import ccxt
import pandas as pd
import redis
from apscheduler.schedulers.background import BackgroundScheduler
from pymongo import MongoClient

import config
from database.db import connect_db
from database.exchange_keys import ExchangeKeys
from tasks.crypto_supertrend_utils.data_loader import DataLoader
from tasks.crypto_supertrend_utils.indicator import calculate_supertrend_talib
from utils.cipher import decrypt_data
from utils.constant import exchange_proxy, get_total_asset_key
from utils.exchange import (
    create_order,
    delete_all_open_orders,
    fetch_positions,
    get_total_asset,
    post_order,
    set_leverage,
)
from utils.log import init_logger

mongo_client = MongoClient(config.MONGODB_URI)
db = mongo_client["investFlow"]

redis = RedisSentinelClient.getInstance()


class StrategyConfig(TypedDict):
    symbols: List[str]
    timeframe: str
    period: int
    multiplier: int
    ratio: float
    decrease: int
    step_count: int
    step_ratio: float


class SupertrendStrategy:
    """
    策略整体实现思路
    1.  k 线定时器，每 25 分钟定时更新一次 K 线数据
    2. 策略触发器，每 0、30 分钟出发一次，出发后检测最新的 K 线与 sp 指标的变化
    """

    def __init__(self, strategy_config: StrategyConfig, exchange_key: ExchangeKeys):
        self.strategy_config = strategy_config
        self.exchange_key = exchange_key
        self.account = exchange_key.account
        self.leverage = 20
        self.ip = get_ip()
        self.total_asset = 0
        self.init_logger()
        self.trade_rules: Dict[str, Any] = {}
        self.trade_record: Dict[str, Any] = {}
        self.trade_record_key = f"investTask:crypto:supertrend_trade_record:{self.exchange_key.account}{self.exchange_key.is_debug and '(debug)' or ''}"
        self.trade_asset_key = f"investTask:crypto:supertrend_trade_asset:{self.exchange_key.account}{self.exchange_key.is_debug and '(debug)' or ''}"
        self.save_klines_lock_key = f"investTask:crypto:supertrend_save_klines_lock:{self.exchange_key.account}{self.exchange_key.is_debug and '(debug)' or ''}"
        self.exchange = self._get_exchange()
        self._init_trade_record()
        self._get_trade_rules()
        self.total_asset = self.update_total_asset()
        self.klines: Dict[str, pd.DataFrame] = {}

    def init_logger(self):
        # 使用loguru的内置日期格式，支持自动按日期切换
        self.logger = init_logger(
            log_file=f"logs/crypto_supertrend/{self.exchange_key.account}{self.exchange_key.is_debug and '(debug)' or ''}/{{time:YYYY-MM-DD}}.log",
            level="INFO",
            console_level="INFO",
            prefix=f"{self.exchange_key.account}{self.exchange_key.is_debug and '(debug)' or ''}",
        )

    def _init_trade_record(self):
        trade_record = redis.get(self.trade_record_key)
        if trade_record:
            self.trade_record = json.loads(trade_record)
            return
        self.logger.info(f"{self.account}未找到redis交易记录")
        send_message(
            f"{self.account}未找到redis交易记录\nip: {self.ip}", title="sp机器人通知"
        )
        trade_record = {}
        for symbol in self.strategy_config["symbols"]:
            trade_record[symbol] = {
                "loss_count": 1,
                "open_price": 0,
            }
        redis.set(self.trade_record_key, json.dumps(trade_record))
        self.trade_record = trade_record

    def _get_exchange(self):
        if self.exchange_key is None:
            return None
        exchange = ccxt.binance(
            {
                "apiKey": self.exchange_key.api_key,
                "secret": decrypt_data(self.exchange_key.api_secret),
                "proxies": {"http": exchange_proxy},
                "options": {
                    "defaultType": "future",  # 使用合约交易
                },
            }
        )
        if self.exchange_key.is_debug:
            self.logger.info(f"{self.account}交易所设置为调试模式")
            exchange.set_sandbox_mode(True)
        return exchange

    def _get_indicators(self):
        """
        检查交易指标
        """
        futures = []
        with ThreadPoolExecutor(max_workers=20) as executor:
            # 提交所有任务
            for symbol in self.strategy_config["symbols"]:
                futures.append(
                    executor.submit(self._check_indicator_for_symbol, symbol)
                )

            # 等待所有任务完成并获取结果
            results = []
            for future in futures:
                results.append(future.result())  # 这会阻塞直到任务完成

        return results

    def _check_indicator_for_symbol(self, symbol):
        end_time = datetime.now() + timedelta(days=2)
        start_time = end_time - timedelta(days=30)

        data_loader = DataLoader()
        data = data_loader.load_crypto_ohlcv(
            symbol,
            self.strategy_config["timeframe"],
            start_time.strftime("%Y-%m-%d"),
            end_time.strftime("%Y-%m-%d"),
        )
        result = calculate_supertrend_talib(
            data,
            period=self.strategy_config["period"],
            multiplier=self.strategy_config["multiplier"],
        )
        last_row = result.iloc[-1]
        last_row_dict = {
            "timestamp": str(int(last_row.name.timestamp())),
            "symbol": symbol,
            **last_row.to_dict(),
        }
        self.klines[symbol] = result.iloc[-300:]
        self.logger.info(
            f"{self.account}交易对:{symbol} 检查指标完成, 收盘价:{last_row['close']}, supertrend:{last_row['supertrend']}, 当前趋势:{last_row['trend']}"
        )
        return last_row_dict

    def should_execute_trade(self, indicators):
        """
        根据返回的指标数据判断是否需要进行下单
        """
        trades = []
        for indicator in indicators:
            symbol = indicator["symbol"]
            trade_trend = self.trade_record.get(symbol, {}).get("direction", None)
            trade_trend = (
                trade_trend == "buy" and 1 or trade_trend == "sell" and -1 or None
            )
            up = indicator["up"]
            down = indicator["down"]
            if up == 1 or down == 1:
                trades.append(indicator)
            elif indicator["trend"] != trade_trend:
                message = f"{self.account}交易对:{symbol} 指标趋势与持仓方向相反\n指标趋势: {indicator['trend']}\n交易记录方向: {trade_trend}\nip: {self.ip}"
                self.logger.info(message)
                send_message(message, title="sp机器人通知")
                trades.append(indicator)
        if trades:

            start_time = datetime.now()
            positions = fetch_positions(self.exchange)
            end_time = datetime.now()
            self.logger.info(f"{self.account}获取持仓耗时: {end_time - start_time}")

            with ThreadPoolExecutor(max_workers=20) as executor:
                futures = []
                for trade in trades:
                    self.logger.info(f"{self.account}{trade['symbol']}有交易信号")
                    futures.append(
                        executor.submit(self._execute_trade, trade, positions)
                    )
                result = []
                for future in futures:
                    result.append(future.result())

                self.logger.info(f"{self.account}交易信号处理完成,结果:{result}")
                # 保存交易记录
                if any(result):
                    self._set_trade_record()
                else:
                    self.logger.info(f"{self.account}本轮无实际交易，跳过记录更新")

        else:
            self.logger.info(f"{self.account}没有交易信号")

    def _clear_future_position(self, positions, symbol):
        for p in positions:
            if p["symbol"] == symbol:
                direction = "SELL" if p["side"] == "long" else "BUY"
                symbol = convert_to_binance_symbol(symbol)
                post_order(
                    self.exchange,
                    {
                        "symbol": symbol,
                        "side": direction,
                        "reduceOnly": True,
                        "type": "MARKET",
                        "quantity": p["contracts"],
                    },
                )

    def _order(self, indicator):
        """
        根据指标信号执行订单

        Args:
            indicator: 指标数据，包含symbol、up、down、close等信息
            positions: 当前持仓信息
        """
        # 获取基本信息
        symbol = indicator["symbol"]
        close_price = indicator["close"]

        # 初始化或获取交易记录
        if symbol not in self.trade_record:
            self.trade_record[symbol] = {
                "open_price": 0,
                "loss_count": 1,
            }

        trade_record = self.trade_record[symbol]
        trade_trend = 1.0 if trade_record["direction"] == "buy" else -1
        # 确定交易方向
        direction = None
        if indicator["up"] == 1:
            direction = "buy"
        elif indicator["down"] == 1:
            direction = "sell"
        # 增加一个 elif 判断，当记录持仓与指标不一致时进行交易，防止指标漂移问题
        elif trade_trend != indicator["trend"]:
            direction = "buy" if indicator["trend"] == 1 else "sell"
        else:
            self.logger.info(f"{self.account}{symbol} 没有交易信号，跳过")
            return

        # 计算订单大小
        trade_rule = self.trade_rules.get(symbol)
        if not trade_rule:
            self.logger.error(f"{self.account}未找到 {symbol} 的交易规则，跳过")
            return

        # 根据方向更新止损计数和执行订单
        self._update_loss_count_and_execute(
            direction, symbol, close_price, trade_record, indicator
        )

    def get_trade_size(self, indicator, symbol, loss_count):
        """
        获取下单头寸
        """
        # 计算交易金额和订单大小生产改成 2
        trade_value = self.total_asset / len(self.strategy_config["symbols"]) / 2
        trade_rule = self.trade_rules[symbol]
        min_notional = trade_rule["minNotional"]

        # 防止除零错误
        atr = max(indicator["atr"], 0.0001)
        adx = max(indicator["adx"], 0.0001)
        size_ratio = self.strategy_config["ratio"] * loss_count / (atr * adx)

        # 确保订单大小符合交易所规则
        amount_precision = trade_rule["amountPrecision"]
        order_size = to_fixed(trade_value * size_ratio, amount_precision)
        min_order_size = max(
            to_fixed(min_notional * 1.2 / indicator["close"], amount_precision),
            get_precision(amount_precision),
        )
        order_size = max(order_size, min_order_size)
        return order_size

    def _update_loss_count_and_execute(
        self, direction, symbol, close_price, trade_record, indicator
    ):
        """
        更新止损计数并执行订单
        """
        open_price = trade_record.get("open_price", 0)
        loss_count = trade_record.get("loss_count", 1)
        origin_loss_count = loss_count  # 日志使用，无其他用途
        trade_size = trade_record.get("order_size", 0)
        decrease = self.strategy_config["decrease"]
        notify_text = f"交易所: {self.exchange_key.exchange}{self.exchange_key.is_debug and '(debug)' or ''}\n"
        notify_text += f"账号: {self.exchange_key.account}\n"
        notify_text += f"交易币种: {symbol}\n"
        notify_text += f"当前持仓: {trade_size}\n"
        notify_text += f"开仓价: {open_price}\n"
        notify_text += f"清仓价: {close_price}\n"
        profit = (
            to_fixed(abs(close_price - open_price) * trade_size, 2)
            if open_price > 0
            else 0
        )
        if direction == "buy":
            # 之前是空单且当前价格高于开仓价，则亏损
            if open_price == 0:
                loss_count = 1
                notify_text += f"🔄 初次建仓，止损计数重置到 {loss_count}\n"
            elif close_price > open_price:
                loss_count += 1
                notify_text += f"🔴 空单亏损: -{profit} USDT 📉\n"
                notify_text += f"⚠️ 止损计数增加到 {origin_loss_count} -> {loss_count}\n"
            else:
                loss_count = max(1, loss_count - decrease)
                notify_text += f"🟢 空单盈利: +{profit} USDT 📈\n"
                notify_text += (
                    f"✅ 止损计数减少到 {origin_loss_count} -> {loss_count}\n"
                )
        elif direction == "sell":
            # 之前是多单且当前价格低于开仓价，则亏损
            if open_price == 0:
                loss_count = 1
                notify_text += f"🔄 初次建仓，止损计数重置到 {loss_count}\n"
            elif close_price < open_price:
                loss_count += 1
                notify_text += f"🔴 多单亏损: -{profit} USDT 📉\n"
                notify_text += f"⚠️ 止损计数增加到 {origin_loss_count} -> {loss_count}\n"
            else:
                loss_count = max(1, loss_count - decrease)
                notify_text += f"🟢 多单盈利: +{profit} USDT 📈\n"
                notify_text += (
                    f"✅ 止损计数减少到 {origin_loss_count} -> {loss_count}\n"
                )
        # 执行订单
        order_size = self.get_trade_size(indicator, symbol, loss_count)
        notify_text += f"下单方向: {direction}\n"
        notify_text += f"下单数量: {order_size}\n"
        notify_text += f"资产计算额度: {to_fixed(self.total_asset, 2)}\n"

        try:
            create_order(
                self.exchange,
                symbol,
                direction,
                order_size,
            )
            now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            notify_text += f"{now} 创建订单成功\n"
            notify_text += f"ip: {self.ip}"
            # 更新交易记录
            self.trade_record[symbol]["open_price"] = close_price
            self.trade_record[symbol]["loss_count"] = loss_count
            self.trade_record[symbol]["direction"] = direction
            self.trade_record[symbol]["order_size"] = order_size
            self.logger.info(notify_text)
            self._set_trade_record()
            send_message(notify_text, title="sp机器人通知")
        except Exception as e:
            self.logger.error(
                f"{self.account}创建订单失败: {symbol} {direction} {order_size}, 错误: {e}"
            )
            notify_text += f"创建订单失败, 下单方向: {direction}, 下单数量: {order_size}, 错误: {e}"
            self.logger.info(notify_text)
            send_message(notify_text, title="sp机器人通知")

    def _get_position(self, positions, symbol):
        for p in positions:
            if p["symbol"] == symbol:
                return p
        return None

    def _is_traded(self, position, indicator):
        if not position:
            return False
        if position["side"] == "long" and indicator["up"] == 1:
            return True
        if position["side"] == "short" and indicator["down"] == 1:
            return True
        return False

    def _execute_trade(self, indicator, positions):
        # 单独设置杠杆倍数
        symbol = indicator["symbol"]
        position = self._get_position(positions, symbol)
        if self._is_traded(position, indicator):
            self.logger.info(f"{self.account}{symbol} 已持仓，跳过")
            return False
        set_leverage(self.exchange, self.leverage, symbol)
        # self.exchange.set_leverage(self.leverage, symbol)
        self.logger.info(f"{self.account}{symbol} 设置杠杆成功: {self.leverage}")
        # 关闭全部订单
        delete_all_open_orders(self.exchange, symbol)
        self.logger.info(f"{self.account}{symbol} 关闭全部订单成功")
        # 持仓方向判断是否需要清仓
        self._clear_future_position(positions, symbol)
        self.logger.info(f"{self.account}{symbol} 清仓成功")

        self._order(indicator)

        return True

        # 保存交易记录

    def update_total_asset(self):
        if self.exchange_key.is_debug:
            asset = get_total_asset(self.exchange, is_debug=True)
            self.total_asset = asset["total_asset"]
            return asset["total_asset"]

        total_asset_key = get_total_asset_key(self.account)
        real_asset = redis.get(total_asset_key)
        trade_asset = float(redis.get(self.trade_asset_key) or 0)

        # 获取资产信息：优先使用缓存，否则从交易所获取
        if real_asset:
            asset = json.loads(real_asset)
        else:
            asset = get_total_asset(self.exchange)

        # 统一处理资产更新和缓存
        self.total_asset = max(asset["total_asset"], trade_asset)
        if self.total_asset != trade_asset:
            self.logger.info(
                f"💰 {self.account} 总资产更新: {self.total_asset:.2f} USDT"
            )
            redis.set(self.trade_asset_key, self.total_asset)
        return self.total_asset

    def _get_leverage_brackets(self, leverage_brackets_info, symbol):
        for bracket in leverage_brackets_info:
            if bracket["symbol"] == symbol:
                return list(
                    map(
                        lambda x: {
                            "initialLeverage": int(x["initialLeverage"]),
                            "notionalCap": int(x["notionalCap"]),
                            "notionalFloor": int(x["notionalFloor"]),
                        },
                        bracket["brackets"],
                    )
                )
        return None

    def _get_trade_rules(self, is_update=False):
        """
        获取每个币种的交易规则,保存到 redis
        """
        redis_key = f"investTask:crypto:tradeRules:binance_futures"
        trade_rules = redis.get(redis_key)
        if trade_rules and not is_update:
            self.trade_rules = json.loads(trade_rules)
            return
        exchange_info = self.exchange.fapipublic_get_exchangeinfo()
        leverage_brackets_info = self.exchange.fapiprivate_get_leveragebracket()
        symbols = exchange_info["symbols"]
        trade_rules = {}
        for s in symbols:
            symbol = s["symbol"]
            contractType = s["contractType"]
            if "USDT" not in symbol or contractType != "PERPETUAL":
                continue
            leverage_brackets = self._get_leverage_brackets(
                leverage_brackets_info, symbol
            )
            symbol = convert_to_ccxt_symbol(symbol)

            # 初始化币种的交易规则
            trade_rule = {
                "pricePrecision": int(s["pricePrecision"]),
                "amountPrecision": int(s["quantityPrecision"]),
                "minNotional": 5,  # 默认值
                "leverage_brackets": leverage_brackets,
            }

            # 遍历过滤器查找MIN_NOTIONAL
            for filter in s["filters"]:
                if filter["filterType"] == "MIN_NOTIONAL":
                    trade_rule["minNotional"] = int(filter["notional"])
                    break

            # 将该币种的交易规则添加到字典中
            trade_rules[symbol] = trade_rule

        redis.set(redis_key, json.dumps(trade_rules))
        print(f"更新交易规则完成，币种数量: {len(trade_rules)}")
        self.trade_rules = trade_rules

    def adjust_position_leverage(self):
        """
        调整持仓杠杆
        """
        positions = self.exchange.fetch_positions()
        for position in positions:
            symbol = position["symbol"]
            leverage_brackets = self.trade_rules[symbol]["leverage_brackets"]
            for bracket in leverage_brackets:
                if (
                    bracket["notionalFloor"]
                    <= position["notional"]
                    <= bracket["notionalCap"]
                ):
                    set_leverage(self.exchange, bracket["initialLeverage"], symbol)
                    # self.exchange.set_leverage(bracket["initialLeverage"], symbol)
                    self.logger.info(
                        f"{self.account}{symbol}调整杠杆成功:  {bracket['initialLeverage']}"
                    )
                    break

    def run(self):
        try:
            self._update_trade_record()
            indicators = self._get_indicators()
            self.should_execute_trade(indicators)
        except Exception as e:
            self._set_trade_record()
            self.logger.error(f"{self.account}运行策略失败: {e}")
            send_message(
                f"{self.account}运行策略失败: {e}\nip: {self.ip}", title="sp机器人通知"
            )

    def _set_trade_record(self):
        """
        设置交易记录到Redis，失败后重试
        """
        try:
            self._set_trade_record_with_retry()
        except Exception as e:
            # 所有重试都失败后，发送通知
            final_error_msg = f"{self.account}redis写入交易记录失败，已重试3次: {e}\n交易记录数据: {self.trade_record}\nip: {self.ip}"
            self.logger.error(final_error_msg)
            send_message(final_error_msg, title="sp机器人通知")

    @retry.retry(Exception, delay=1, tries=3, logger=None)
    def _set_trade_record_with_retry(self):
        """
        实际执行Redis写入的方法，带重试装饰器
        """
        redis.set(self.trade_record_key, json.dumps(self.trade_record))
        self.logger.info(
            f"{self.account}交易记录写入Redis成功，交易记录: {self.trade_record}"
        )

    def _update_trade_record(self):
        try:
            self._update_trade_record_with_retry()
        except Exception as e:
            send_message(
                f"{self.account}redis更新交易记录失败: {e}\nip: {self.ip}",
                title="sp机器人通知",
            )
            self.logger.error(f"{self.account}redis更新交易记录失败: {e}")

    @retry.retry(Exception, delay=1, tries=3, logger=None)
    def _update_trade_record_with_retry(self):
        """
        实际执行Redis写入的方法，带重试装饰器
        """
        trade_record = redis.get(self.trade_record_key)
        if trade_record:
            self.trade_record = json.loads(trade_record)
            self.logger.info(
                f"{self.account}redis交易记录更新成功, 交易记录: {self.trade_record}"
            )
        else:
            send_message(
                f"{self.account}redis更新交易记录失败: 交易记录不存在\nip: {self.ip}",
                title="sp机器人通知",
            )
            self.logger.error(f"{self.account}redis更新交易记录失败: 交易记录不存在")


class SupertrendStrategyController:
    """
    策略控制器
    1. 更新账号资金使用串行方式
    2.保存 K 线数据内部实现
    3.指标检测，数据获取内部实现，之后转并发各自处理交易逻辑
    """

    def __init__(self, is_debug):
        self.exchange_keys = []
        self.is_debug = is_debug
        # 策略示例列表
        self.strategies = []
        self.configs = []
        self.check_and_trade_lock_key = (
            f"investTask:crypto:supertrend_check_and_trade_lock"
        )
        self.save_klines_lock_key = f"investTask:crypto:supertrend_save_klines_lock"
        self._init_config()
        self._init_exchange_keys()
        self._init_strategies()

    def _init_exchange_keys(self):
        connect_db()
        if self.is_debug:
            self.exchange_keys = [
                key for key in ExchangeKeys.objects().all() if key.is_debug
            ]
        else:
            self.exchange_keys = [
                key for key in ExchangeKeys.objects().all() if not key.is_debug
            ]

    def _init_strategies(self):
        for config in self.configs:
            exchange_key = config.exchange_key
            strategy = SupertrendStrategy(config, exchange_key)
            self.strategies.append(strategy)

    def _init_config(self):
        connect_db()
        configs = CryptoSupertrend.objects().all()
        self.configs = configs

    def update_total_asset(self):
        for strategy in self.strategies:
            strategy.update_total_asset()

    def check_and_trade(self):
        with ThreadPoolExecutor(max_workers=20) as executor:
            for strategy in self.strategies:
                strategy_config = strategy.strategy_config
                futures = []
                for symbol in strategy_config["symbols"]:
                    end_time = datetime.now() + timedelta(days=2)
                    start_time = end_time - timedelta(days=30)

                    data_loader = DataLoader()
                    futures.append(
                        executor.submit(
                            data_loader.load_crypto_ohlcv,
                            symbol,
                            strategy_config["timeframe"],
                            start_time.strftime("%Y-%m-%d"),
                            end_time.strftime("%Y-%m-%d"),
                        )
                    )
                for future in futures:
                    future.result()
        with redis.lock(self.check_and_trade_lock_key, timeout=60):

            with ThreadPoolExecutor(max_workers=10) as executor:
                futures = []
                for strategy in self.strategies:
                    futures.append(executor.submit(strategy.run))

                for future in futures:
                    future.result()

    def merge_klines(self, klines):

        # 合并所有k线数据，按时间对齐
        # 选择第一个数据集作为基准
        base_df = klines[0].copy()
        # 合并 supertrend 数据
        supertrend_list = []
        for index in range(len(base_df)):
            supertrend_entry = [
                {
                    "period": int(df["period"].iloc[index]),
                    "multiplier": int(df["multiplier"].iloc[index]),
                    "value": float(df["supertrend"].iloc[index]),
                    "trend": int(df["trend"].iloc[index]),
                    "up": int(df["up"].iloc[index]),
                    "down": int(df["down"].iloc[index]),
                }
                for df in klines
            ]
            supertrend_list.append(supertrend_entry)

        base_df["supertrend"] = supertrend_list
        result_df = base_df[["open", "high", "low", "close", "volume", "supertrend"]]
        return result_df

    def _get_klines_collection(self):
        collection_name = "crypto_supertrend_klines"
        try:
            # 尝试创建时间序列集合
            db.create_collection(
                collection_name,
                timeseries={
                    "timeField": "timestamp",  # 时间戳字段
                    "metaField": "symbol",  # 元数据字段（币种）
                    "granularity": "minutes",  # 假设 1 分钟 K 线
                },
                expireAfterSeconds=30 * 60 * 300,  # 数据保留300条 30 分钟数据
            )
        except CollectionInvalid:
            pass

        return db[collection_name]

    def save_klines(self):
        with redis.lock(self.save_klines_lock_key, timeout=60):
            klines = {}
            for strategy in self.strategies:
                for symbol, kline in strategy.klines.items():
                    if symbol not in klines:
                        klines[symbol] = [kline]
                    else:
                        klines[symbol].append(kline)

            collection = self._get_klines_collection()
            for symbol, klines in klines.items():
                result_df = self.merge_klines(klines)
                max_ts = collection.find_one(
                    {"symbol": symbol}, sort=[("timestamp", -1)]
                )
                # 将索引重置为列，这样timestamp就变成了一个普通列
                kline_reset = result_df.reset_index()
                kline_reset["symbol"] = symbol
                max_ts = max_ts["timestamp"] if max_ts else 0
                if max_ts == 0:
                    collection.insert_many(kline_reset.to_dict("records"))
                    print(
                        f"保存k线数据完成，symbol:{symbol}, 保存条数:{len(kline_reset)}"
                    )

                else:
                    max_ts = pd.to_datetime(max_ts, utc=True)
                    kline_filtered = kline_reset[kline_reset["timestamp"] > max_ts]
                    if kline_filtered.empty:
                        print(f"k线数据已存在，symbol:{symbol}, 最新时间:{max_ts}")
                        continue
                    collection.insert_many(kline_filtered.to_dict("records"))
                    print(
                        f"保存k线数据完成，symbol:{symbol}, 保存条数:{len(kline_filtered)}"
                    )

    def set_trade_rules(self):
        """
        获取每个币种的交易规则,保存到 redis
        """
        self.strategies[0]._get_trade_rules(is_update=True)

    def update_trade_record(self):
        for strategy in self.strategies:
            strategy._update_trade_record()

    def adjust_position_leverage(self):
        for strategy in self.strategies:
            strategy.adjust_position_leverage()

    def run(self):
        self.update_total_asset()
        self.check_and_trade()
        self.save_klines()


if __name__ == "__main__":
    # 创建调度器
    scheduler = BackgroundScheduler()
    controller = SupertrendStrategyController(is_debug=True)
    controller.set_trade_rules()
    controller.adjust_position_leverage()
    controller.run()
    scheduler.add_job(controller.update_total_asset, "interval", minutes=1)
    scheduler.add_job(
        controller.check_and_trade,
        "cron",
        minute="0, 30",
        second="1",
        max_instances=1,
    )
    # 对一分钟可能失败的情况进行兜底
    scheduler.add_job(
        controller.check_and_trade,
        "cron",
        minute="3,4,5,10,15,20,25,32,33,34,35,40,45,50,55",
        max_instances=1,
    )
    scheduler.add_job(controller.save_klines, "cron", minute="1, 31", max_instances=1)
    scheduler.add_job(controller.set_trade_rules, "interval", hours=1)
    scheduler.add_job(
        controller.adjust_position_leverage, "cron", minute="1, 31", second="30"
    )
    scheduler.add_job(controller.update_trade_record, "cron", minute="58, 28")
    scheduler.start()

    try:
        while True:
            time.sleep(1)
    except (KeyboardInterrupt, SystemExit):
        scheduler.shutdown()
        print("调度器已关闭")
