import os
import sys


# 将项目根目录添加到 Python 路径
project_root = os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
)
sys.path.insert(0, project_root)

import ccxt
from numpy.random import f
import pandas as pd
from datetime import datetime
import pytz
import concurrent.futures
from dateutil.parser import parse
import requests
import logging
import sys
from utils.constant import exchange_proxy

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)],
)
logger = logging.getLogger(__name__)


def get_partition_path(symbol, timeframe, year, month):
    """
    生成加密货币分区路径

    Args:
        symbol: 交易对
        timeframe: 时间周期
        year: 年份
        month: 月份

    Returns:
        分区文件路径
    """
    symbol_clean = symbol.replace("/", "")
    return f"data/crypto/{symbol_clean}/{timeframe}/{year}-{month:02d}.parquet"


def parse_datetime(date_str, is_utc=True):
    """
    解析字符串日期，支持 YYYY-mm-dd 或 YYYY-mm-dd hh:mm:ss

    Args:
        date_str: 日期字符串

    Returns:
        解析后的 UTC 时间戳
    """
    try:
        return pd.to_datetime(parse(date_str), utc=is_utc)
    except Exception as e:
        raise ValueError(
            f"Invalid date format: {date_str}. Use YYYY-mm-dd or YYYY-mm-dd hh:mm:ss"
        )


class CryptoDataProvider:
    """加密货币数据提供者，用于获取和存储 K 线数据"""

    def __init__(self, exchange_id="binance", exchange_params=None):
        """
        初始化加密货币数据提供者

        Args:
            exchange_id: 交易所 ID，默认为 'binance'
            exchange_params: 交易所参数，默认为 {'enableRateLimit': True}
        """
        if exchange_params is None:
            exchange_params = {"enableRateLimit": True}

        self.exchange = getattr(ccxt, exchange_id)(exchange_params)
        self.logger = logger

    def get_futures_klines(
        self,
        symbol="BTCUSDT",
        interval="30m",
        limit=1000,
        contract_type="PERPETUAL",
        start_ms=None,
    ):
        """
        从币安 USDT-M 合约获取 K 线数据
        :param symbol: 交易对，例如 'BTCUSDT'
        :param interval: K 线时间间隔，例如 '1h'（1小时）、'1m'（1分钟）、'1d'（1天）
        :param limit: 获取的 K 线数量，最大 1500
        :param contract_type: 合约类型，例如 'PERPETUAL'（永续合约）
        :return: K 线数据列表
        """
        # 币安 USDT-M 合约 API 端点
        url = "https://fapi.binance.com/fapi/v1/klines"

        # 请求参数
        params = {
            "symbol": symbol,
            "interval": interval,
            "limit": limit,
            "contractType": contract_type,  # 可选，默认为 PERPETUAL
            "startTime": start_ms,
        }

        proxies = {
            "http": exchange_proxy,
            "https": exchange_proxy,
        }

        try:
            # 发送 GET 请求
            response = requests.get(url, params=params, proxies=proxies)
            response.raise_for_status()  # 检查请求是否成功

            # 解析 JSON 数据
            klines = response.json()

            # # 格式化输出
            # for kline in klines:
            #     open_time = time.strftime('%Y-%m-%d %H:%M:%S', time.gmtime(kline[0] / 1000))
            #     open_price = kline[1]
            #     high_price = kline[2]
            #     low_price = kline[3]
            #     close_price = kline[4]
            #     volume = kline[5]
            #     print(f"开盘时间: {open_time}, 开盘价: {open_price}, 最高价: {high_price}, "
            #         f"最低价: {low_price}, 收盘价: {close_price}, 成交量: {volume}")

            return [item[:6] for item in klines]

        except requests.exceptions.RequestException as e:
            logger.error(f"请求失败: {e}")
            raise e

    def fetch_ohlcv(self, symbol, timeframe, start_time, end_time, limit=1000):
        """
        获取指定时间段的 K 线数据

        Args:
            symbol: 交易对
            timeframe: 时间周期
            start_time: 开始时间
            end_time: 结束时间
            limit: 每次请求的数据量限制

        Returns:
            DataFrame 格式的 K 线数据
        """
        self.logger.debug(
            f"获取数据: {symbol} {timeframe} 从 {start_time} 到 {end_time}"
        )
        start_ms = int(start_time.timestamp() * 1000)
        end_ms = int(end_time.timestamp() * 1000)
        ohlcv = []

        while start_ms < end_ms:
            try:
                kline_symbol = symbol.replace("/", "").replace(":USDT", "")
                data = self.get_futures_klines(
                    kline_symbol, timeframe, limit, start_ms=start_ms
                )
                if not data:
                    break
                ohlcv.extend(data)
                start_ms = data[-1][0] + 1
                self.logger.debug(
                    f"  已获取 {len(ohlcv)} 条数据，当前时间: {datetime.fromtimestamp(start_ms/1000, tz=pytz.UTC)}"
                )
            except Exception as e:
                logger.error(f"Error fetching data: {e}")
                raise e

        df = pd.DataFrame(
            ohlcv, columns=["timestamp", "open", "high", "low", "close", "volume"]
        ).astype(
            {
                "timestamp": int,
                "open": float,
                "high": float,
                "low": float,
                "close": float,
                "volume": float,
            }
        )
        df["timestamp"] = pd.to_datetime(df["timestamp"], unit="ms", utc=True)
        result_df = df[(df["timestamp"] >= start_time) & (df["timestamp"] <= end_time)]
        self.logger.debug(f"  最终获取 {len(result_df)} 条有效数据")
        return result_df

    def fetch_ohlcv_segment(
        self, symbol, timeframe, segment_start, segment_end, limit=1000
    ):
        """
        多线程辅助函数：获取单个时间段的数据

        Args:
            symbol: 交易对
            timeframe: 时间周期
            segment_start: 片段开始时间
            segment_end: 片段结束时间
            limit: 每次请求的数据量限制

        Returns:
            DataFrame 格式的 K 线数据
        """
        return self.fetch_ohlcv(symbol, timeframe, segment_start, segment_end, limit)

    def merge_ohlcv(self, existing_df, new_df):
        """
        合并新旧数据，去重并排序

        Args:
            existing_df: 现有数据
            new_df: 新数据

        Returns:
            合并后的数据
        """
        if existing_df.empty:
            return new_df
        combined_df = pd.concat([existing_df, new_df], ignore_index=True)
        combined_df = combined_df.drop_duplicates(subset="timestamp", keep="last")
        return combined_df.sort_values("timestamp")

    def insert_ohlcv_to_parquet(self, symbol, timeframe, start_time, end_time):
        """
        插入 K 线数据到分区 Parquet 文件

        Args:
            symbol: 交易对
            timeframe: 时间周期
            start_time: 开始时间
            end_time: 结束时间
        """
        # 1. 初始化和时间解析
        self.logger.debug(f"\n开始处理: {symbol} {timeframe} 数据")
        self.logger.debug(f"请求时间范围: {start_time} 到 {end_time}")

        start_time = parse_datetime(start_time)
        current_time = pd.Timestamp.now(tz="UTC")
        end_time = parse_datetime(end_time)
        self.logger.debug(f"解析后时间范围: {start_time} 到 {end_time}")

        # 限制结束时间不超过当前时间
        if end_time > current_time:
            self.logger.debug(
                f"警告: 结束时间 ({end_time}) 在未来。限制为当前时间 ({current_time})。"
            )
            end_time = current_time

        # 2. 解析时间周期
        timeframe_minutes = self._get_timeframe_minutes(timeframe)

        # 3. 确定需要处理的月度分区
        partitions = self._get_monthly_partitions(start_time, end_time)
        self.logger.debug(f"需要处理的分区: {len(partitions)} 个月度分区")

        # 4. 分析每个分区，确定需要获取的数据段
        fetch_segments = self._analyze_partitions(
            partitions,
            symbol,
            timeframe,
            start_time,
            end_time,
            current_time,
            timeframe_minutes,
        )

        # 5. 获取缺失数据
        new_data = self._fetch_missing_data(symbol, timeframe, fetch_segments)

        # 6. 合并并保存数据
        self._merge_and_save_data(new_data, partitions, symbol, timeframe)

        self.logger.debug(f"数据处理完成: {symbol} {timeframe}")

    def _get_timeframe_minutes(self, timeframe):
        """解析时间周期，返回对应的分钟数"""
        if timeframe.endswith("m"):
            return int(timeframe[:-1])
        elif timeframe.endswith("h"):
            return int(timeframe[:-1]) * 60
        elif timeframe.endswith("d"):
            return int(timeframe[:-1]) * 1440
        else:
            raise ValueError(f"不支持的时间周期格式: {timeframe}")

    def _get_monthly_partitions(self, start_time, end_time):
        """确定时间范围内的所有年/月分区"""
        current = start_time
        partitions = set()
        while current <= end_time:
            partitions.add((current.year, current.month))
            current = current + pd.offsets.MonthBegin(1)
        return partitions

    def _align_to_timeframe(self, timestamp, timeframe_minutes):
        """将时间对齐到K线周期"""
        minutes_since_day_start = timestamp.hour * 60 + timestamp.minute
        aligned_minutes = (
            minutes_since_day_start // timeframe_minutes
        ) * timeframe_minutes
        return timestamp.replace(
            hour=aligned_minutes // 60,
            minute=aligned_minutes % 60,
            second=0,
            microsecond=0,
        )

    def _analyze_partitions(
        self,
        partitions,
        symbol,
        timeframe,
        start_time,
        end_time,
        current_time,
        timeframe_minutes,
    ):
        """分析每个分区，确定需要获取的数据段"""
        fetch_segments = []

        for year, month in partitions:
            # 计算分区的开始和结束时间
            partition_start = pd.Timestamp(year=year, month=month, day=1, tz="UTC")
            partition_end = (partition_start + pd.offsets.MonthEnd(0)).replace(
                hour=23, minute=59, second=59
            )
            partition_end = min(partition_end, end_time)
            partition_start = max(partition_start, start_time)

            # 获取分区文件路径
            parquet_file = get_partition_path(symbol, timeframe, year, month)

            # 检查分区文件是否存在
            if not os.path.exists(parquet_file):
                self.logger.debug(f"分区 {year}-{month:02d} 不存在，将获取完整数据")
                fetch_segments.append((partition_start, partition_end))
                continue

            # 读取现有分区数据
            self.logger.debug(f"分区 {year}-{month:02d} 已存在，检查数据完整性")
            existing_df = pd.read_parquet(parquet_file)

            # 处理空文件情况
            if existing_df.empty:
                self.logger.debug(
                    f"分区 {year}-{month:02d} 文件存在但为空，将获取完整数据"
                )
                fetch_segments.append((partition_start, partition_end))
                continue

            # 转换时间戳格式
            existing_df["timestamp"] = pd.to_datetime(
                existing_df["timestamp"], utc=True
            )
            existing_start = existing_df["timestamp"].min()
            existing_end = existing_df["timestamp"].max()

            # 检查是否需要补充开始部分的数据
            if partition_start < existing_start:
                self.logger.debug(
                    f"  需要补充 {partition_start} 到 {existing_start} 的数据"
                )
                fetch_segments.append(
                    (partition_start, min(existing_start, partition_end))
                )

            # 计算理论上的最后一个K线时间
            last_day = partition_start + pd.offsets.MonthEnd(0)
            last_day_end = last_day.replace(hour=23, minute=59, second=59)
            expected_last_candle = self._align_to_timeframe(
                last_day_end, timeframe_minutes
            )

            # 检查是否需要补充结束部分的数据
            if existing_end < expected_last_candle:
                # 当前月份特殊处理
                if year == current_time.year and month == current_time.month:
                    # 计算当前时间对应的最后一个完整K线
                    current_last_candle = self._align_to_timeframe(
                        current_time - pd.Timedelta(minutes=timeframe_minutes),
                        timeframe_minutes,
                    )
                    if existing_end < current_last_candle:
                        self.logger.debug(
                            f"  需要补充 {existing_end} 到 {current_last_candle} 的数据"
                        )
                        fetch_segments.append(
                            (max(existing_end, partition_start), current_last_candle)
                        )
                else:
                    # 历史月份补充到月末最后一个K线
                    self.logger.debug(
                        f"  需要补充 {existing_end} 到 {expected_last_candle} 的数据"
                    )
                    fetch_segments.append(
                        (max(existing_end, partition_start), expected_last_candle)
                    )

        return fetch_segments

    def _fetch_missing_data(self, symbol, timeframe, fetch_segments):
        """使用多线程获取缺失数据"""
        new_data = []

        if not fetch_segments:
            self.logger.debug("所有分区数据已完整，无需获取新数据")
            return new_data

        self.logger.debug(f"开始获取 {len(fetch_segments)} 个时间段的数据")
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [
                executor.submit(
                    self.fetch_ohlcv_segment,
                    symbol,
                    timeframe,
                    segment_start,
                    segment_end,
                )
                for segment_start, segment_end in fetch_segments
            ]
            for future in concurrent.futures.as_completed(futures):
                segment_data = future.result()
                if not segment_data.empty:
                    new_data.append(segment_data)

        return new_data

    def _merge_and_save_data(self, new_data, partitions, symbol, timeframe):
        """合并并保存数据到各个分区"""
        if not new_data:
            self.logger.debug("没有新数据需要保存")
            return

        # 合并所有新获取的数据
        new_df = pd.concat(new_data, ignore_index=True)
        logger.info(f"「{symbol} {timeframe}」共获取 {len(new_df)} 条新数据")

        # 按分区处理数据
        for year, month in partitions:
            parquet_file = get_partition_path(symbol, timeframe, year, month)
            os.makedirs(os.path.dirname(parquet_file), exist_ok=True)

            # 读取现有分区数据
            existing_df = pd.DataFrame()
            if os.path.exists(parquet_file):
                existing_df = pd.read_parquet(parquet_file)
                existing_df["timestamp"] = pd.to_datetime(
                    existing_df["timestamp"], utc=True
                )
                self.logger.debug(
                    f"分区 {year}-{month:02d} 现有 {len(existing_df)} 条数据"
                )

            # 过滤当前分区的数据
            partition_start = pd.Timestamp(year=year, month=month, day=1, tz="UTC")
            partition_end = (partition_start + pd.offsets.MonthEnd(0)).replace(
                hour=23, minute=59, second=59
            )
            partition_data = new_df[
                (new_df["timestamp"] >= partition_start)
                & (new_df["timestamp"] <= partition_end)
            ]

            # 合并并存储
            combined_df = self.merge_ohlcv(existing_df, partition_data)
            if not combined_df.empty:
                self.logger.debug(
                    f"保存分区 {year}-{month:02d}，共 {len(combined_df)} 条数据"
                )
                combined_df.to_parquet(parquet_file, index=False, engine="pyarrow")


if __name__ == "__main__":
    provider = CryptoDataProvider(exchange_id="binance")
    symbol = "BTC/USDT:USDT"
    timeframe = "30m"
    start_time = "2025-05-01"
    end_time = "2025-12-31 23:59:59"  # 未来时间
    provider.insert_ohlcv_to_parquet(symbol, timeframe, start_time, end_time)
