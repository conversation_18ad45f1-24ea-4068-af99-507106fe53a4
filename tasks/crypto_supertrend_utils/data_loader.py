import os
import pandas as pd
import logging
import sys
from .crypto_data_provider import CryptoDataProvider, get_partition_path, parse_datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

class DataLoader:
    def __init__(self):
        self.crypto_provider = CryptoDataProvider()
        self.logger = logger

    def load_crypto_ohlcv(self, symbol, timeframe, start_time, end_time):
        self.logger.debug(f"开始加载数据: 交易对={symbol}, 时间周期={timeframe}, 开始时间={start_time}, 结束时间={end_time}")
        self.crypto_provider.insert_ohlcv_to_parquet(symbol, timeframe, start_time, end_time)
        # 解析时间字符串
        start_time = parse_datetime(start_time)
        end_time = parse_datetime(end_time)
        self.logger.debug(f"解析后的时间范围: {start_time} 至 {end_time}")

        # 限制 end_time 不超过当前时间
        current_time = pd.Timestamp.now(tz="UTC")
        if end_time > current_time:
            self.logger.debug(
                f"警告: 结束时间 ({end_time}) 在未来。限制为当前时间 ({current_time})."
            )
            end_time = current_time

        # 确定涉及的年/月分区
        current = start_time
        partitions = set()
        while current <= end_time:
            partitions.add((current.year, current.month))
            current = current + pd.offsets.MonthBegin(1)
        self.logger.debug(f"需要加载的分区: {partitions}")

        # 加载分区数据
        data_frames = []
        for year, month in partitions:
            parquet_file = get_partition_path(symbol, timeframe, year, month)
            self.logger.debug(f"尝试加载分区文件: {parquet_file}")
            if os.path.exists(parquet_file):
                df = pd.read_parquet(parquet_file)
                df["timestamp"] = pd.to_datetime(df["timestamp"], utc=True)
                data_frames.append(df)
                self.logger.debug(f"成功加载分区 {year}-{month:02d}, 数据行数: {len(df)}")
            else:
                self.logger.debug(f"分区文件不存在: {parquet_file}")

        # 合并数据
        if not data_frames:
            raise ValueError(
                f"未找到 {symbol} ({timeframe}) 在 {start_time} 和 {end_time} 之间的数据"
            )

        combined_df = pd.concat(data_frames, ignore_index=True)
        self.logger.debug(f"合并后的原始数据行数: {len(combined_df)}")
        
        # 显示合并后数据的时间范围
        if not combined_df.empty:
            self.logger.debug(f"合并后数据的时间范围: {combined_df['timestamp'].min()} 到 {combined_df['timestamp'].max()}")
        
        # 过滤时间范围外的数据
        filtered_df = combined_df[
            (combined_df["timestamp"] >= start_time)
            & (combined_df["timestamp"] <= end_time)
        ]
        
        # 计算被过滤掉的数据量
        filtered_out = len(combined_df) - len(filtered_df)
        if filtered_out > 0:
            self.logger.debug(f"过滤掉 {filtered_out} 条时间范围外的数据:")
            
            # 显示被过滤掉的数据的时间范围
            too_early = combined_df[combined_df["timestamp"] < start_time]
            too_late = combined_df[combined_df["timestamp"] > end_time]
            
            if not too_early.empty:
                self.logger.debug(f"  - 早于开始时间的数据: {len(too_early)} 条，时间范围: {too_early['timestamp'].min()} 到 {too_early['timestamp'].max()}")
            
            if not too_late.empty:
                self.logger.debug(f"  - 晚于结束时间的数据: {len(too_late)} 条，时间范围: {too_late['timestamp'].min()} 到 {too_late['timestamp'].max()}")
        
        combined_df = filtered_df
        self.logger.debug(f"时间过滤后的数据行数: {len(combined_df)}")
        
        # 检查是否存在重复的时间戳
        duplicate_count = combined_df.duplicated(subset="timestamp").sum()
        if duplicate_count > 0:
            self.logger.debug(f"发现 {duplicate_count} 条重复数据，执行去重操作")
            combined_df = combined_df.drop_duplicates(subset="timestamp", keep="last")
            self.logger.debug(f"去重后的数据行数: {len(combined_df)}")
        else:
            self.logger.debug("数据中没有重复的时间戳，无需去重")
        
        combined_df = combined_df.sort_values("timestamp")

        # 转换为 backtrader 格式
        combined_df = combined_df[
            ["timestamp", "open", "high", "low", "close", "volume"]
        ]
        combined_df.set_index("timestamp", inplace=True)

        # 检查数据完整性
        if combined_df.empty:
            raise ValueError(
                f"过滤后无有效数据: {symbol} ({timeframe})"
            )

        # 对齐时间框架并填充缺失数据
        expected_freq = pd.Timedelta(timeframe)
        combined_df = combined_df.asfreq(expected_freq, method="ffill")
        self.logger.debug(f"重采样后的数据行数: {len(combined_df)}")
        self.delete_expire_data(symbol, timeframe)

        return combined_df.iloc[-1000:]

    def delete_expire_data(self, symbol, timeframe):
        try:
            # 获取结果目录路径
            symbol_clean = symbol.replace('/', '')
            results_dir = f"data/crypto/{symbol_clean}/{timeframe}"
            if not os.path.exists(results_dir):
                return
                
            # 获取所有CSV文件
            csv_files = []
            for file in os.listdir(results_dir):
                if file.endswith('.parquet'):
                    file_path = os.path.join(results_dir, file)
                    # 获取文件修改时间
                    mod_time = os.path.getmtime(file_path)
                    csv_files.append((file_path, mod_time))
            
            # 按修改时间排序
            csv_files.sort(key=lambda x: x[1], reverse=True)
            
            # 删除旧文件，只保留最新的2个
            if len(csv_files) > 2:
                for file_path, _ in csv_files[2:]:
                    os.remove(file_path)
                    logger.info(f"删除过期数据: {file_path}")
        except Exception as e:
            self.logger.debug(f"删除过期数据时出错: {str(e)}")

            
# 示例使用
if __name__ == "__main__":
    # provider = CryptoDataProvider(exchange_id='binance')
    # symbol = 'BNB/USDT:USDT'
    # timeframe = '30m'
    # start_time = '2025-05-01'
    # end_time = '2025-12-31 23:59:59'  # 未来时间
    # provider.insert_ohlcv_to_parquet(symbol, timeframe, start_time, end_time)

    data_loader = DataLoader()
    data = data_loader.load_crypto_ohlcv("BTC/USDT:USDT", "30m", "2025-05-01", "2025-07-01")
    print(data)