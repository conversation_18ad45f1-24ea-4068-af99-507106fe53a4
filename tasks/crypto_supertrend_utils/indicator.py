from datetime import datetime, timedelta
import timeit
import numpy as np
import talib
import pandas as pd

from tasks.crypto_supertrend_utils.data_loader import DataLoader

def calculate_supertrend_talib(df, period=10, multiplier=3):
    """
    使用 TA-Lib 计算 SuperTrend 指标，与 Backtrader 实现一致。
    
    参数:
        df: DataFrame，包含 'open', 'high', 'low', 'close' 列
        period: ATR 计算周期（默认 10）
        multiplier: ATR 乘数（默认 3）
    
    返回:
        DataFrame，包含以下列：
        - median: 中价
        - atr: ATR 值
        - upper_band: 上轨
        - lower_band: 下轨
        - supertrend: SuperTrend 主线
        - trend: 趋势方向 (1: 上涨, -1: 下跌)
        - up: 上穿信号 (1: 触发, 0: 未触发)
        - down: 下穿信号 (1: 触发, 0: 未触发)
    """
    # 复制输入数据
    result = df.copy()
    result['adx'] = talib.ADX(
        high=result['high'].values,
        low=result['low'].values,
        close=result['close'].values,
    )
    
    # 确保数据类型是 float64 (double)
    result['high'] = result['high'].astype(np.float64)
    result['low'] = result['low'].astype(np.float64)
    result['close'] = result['close'].astype(np.float64)
    result['open'] = result['open'].astype(np.float64)
    
    # 计算中价
    result['median'] = (result['high'] + result['low'] + result['close'] + result['open']) / 4
    
    # 使用 TA-Lib 计算 ATR
    result['atr'] = talib.ATR(result['high'].values, result['low'].values, result['close'].values, timeperiod=period)
    
    # 计算上下轨道
    result['upper_band'] = result['median'] + multiplier * result['atr']
    result['lower_band'] = result['median'] - multiplier * result['atr']
    
    # 初始化输出列
    result['supertrend'] = np.nan
    result['trend'] = 1  # 默认初始趋势为上涨
    result['up'] = 0
    result['down'] = 0
    result['period'] = period
    result['multiplier'] = multiplier
    
    # 逐行计算 SuperTrend
    for i in range(1, len(result)):
        prev_close = result['close'].iloc[i-1]
        prev_supertrend = result['supertrend'].iloc[i-1]
        
        # 如果 ATR 或中价为 NaN，跳过
        if pd.isna(result['atr'].iloc[i]) or pd.isna(result['median'].iloc[i]):
            continue
        
        # 确定当前趋势
        if not pd.isna(prev_supertrend) and prev_supertrend <= prev_close:
            result.loc[result.index[i], 'trend'] = 1  # 上涨趋势
        else:
            result.loc[result.index[i], 'trend'] = -1  # 下跌趋势
        
        # 根据趋势更新 SuperTrend
        if result['trend'].iloc[i] == 1:
            supertrend_val = max(
                result['lower_band'].iloc[i],
                result['supertrend'].iloc[i-1] if not pd.isna(prev_supertrend) else result['lower_band'].iloc[i]
            )
            result.loc[result.index[i], 'supertrend'] = supertrend_val
            
            # 检查是否反转为下跌趋势
            if result['close'].iloc[i] < supertrend_val:
                result.loc[result.index[i], 'trend'] = -1
                result.loc[result.index[i], 'supertrend'] = result['upper_band'].iloc[i]
        else:
            supertrend_val = min(
                result['upper_band'].iloc[i],
                result['supertrend'].iloc[i-1] if not pd.isna(prev_supertrend) else result['upper_band'].iloc[i]
            )
            result.loc[result.index[i], 'supertrend'] = supertrend_val
            
            # 检查是否反转为上涨趋势
            if result['close'].iloc[i] > supertrend_val:
                result.loc[result.index[i], 'trend'] = 1
                result.loc[result.index[i], 'supertrend'] = result['lower_band'].iloc[i]
        
        # 生成交易信号
        prev_trend = result['trend'].iloc[i-1]
        if not pd.isna(prev_trend):
            result.loc[result.index[i], 'up'] = 1 if result['trend'].iloc[i] == 1 and prev_trend == -1 else 0
            result.loc[result.index[i], 'down'] = 1 if result['trend'].iloc[i] == -1 and prev_trend == 1 else 0
    
    # 设置第一个有效 SuperTrend 值
    first_valid_idx = result['atr'].first_valid_index()
    if first_valid_idx is not None:
        result.loc[first_valid_idx, 'supertrend'] = result.loc[first_valid_idx, 'lower_band']
    
    return result

if __name__ == "__main__":

    def run():
        end_time = datetime.now()
        start_time = end_time - timedelta(days=30)

        data_loader = DataLoader()
        data = data_loader.load_crypto_ohlcv("ETH/USDT:USDT", "30m", start_time.strftime('%Y-%m-%d'), end_time.strftime('%Y-%m-%d'))
        result = calculate_supertrend_talib(data, period=10, multiplier=3)
        print(result)

    execution_time = timeit.timeit(run, number=1)
    print(f"Execution time: {execution_time} seconds")