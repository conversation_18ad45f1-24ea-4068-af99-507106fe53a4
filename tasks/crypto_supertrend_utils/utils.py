import pandas as pd

def merge_multiple_kline_data(data_list):
    # 确保数据列表非空
    if not data_list:
        return [['time', 'open', 'high', 'low', 'close', 'atr']]
    
    # 转换为 DataFrame，时间作为索引，第一行作为列名
    dfs = [pd.DataFrame(data[1:], columns=data[0]).set_index('time') for data in data_list]
    
    # 合并所有 DataFrame，按时间对齐
    merged_df = dfs[0]
    for df in dfs[1:]:
        merged_df = merged_df.join(df, rsuffix=f'_{len(merged_df.columns) // 5}', how='inner')
    
    # 提取 ATR 列并合并为字典
    atr_cols = [col for col in merged_df.columns if col.startswith('atr')]
    merged_df['atr'] = merged_df[atr_cols].to_dict(orient='records')
    
    # 保留第一个数据源的 OHLC 数据，删除多余 ATR 列
    ohlc_cols = ['open', 'high', 'low', 'close']
    base_cols = [f'{col}_0' if col in ohlc_cols else col for col in dfs[0].columns]
    result_df = merged_df[base_cols + ['atr']].rename(columns={f'{col}_0': col for col in ohlc_cols})
    
    # 转换为目标格式列表
    result = [result_df.columns.tolist()] + result_df.reset_index().values.tolist()
    
    return result

if __name__ == "__main__":
    # 示例数据
    data_a = [
        ['time', 'open', 'high', 'low', 'close', 'atr'],
        ['2025-06-26 08:38', 100, 105, 98, 102, 2.5],
        ['2025-06-26 08:43', 102, 107, 100, 104, 2.7]
    ]
    data_b = [
        ['time', 'open', 'high', 'low', 'close', 'atr'],
        ['2025-06-26 08:38', 101, 106, 99, 103, 2.8],
        ['2025-06-26 08:43', 103, 108, 101, 105, 3.0]
    ]
    data_c = [
        ['time', 'open', 'high', 'low', 'close', 'atr'],
        ['2025-06-26 08:38', 99, 104, 97, 101, 2.6],
        ['2025-06-26 08:43', 101, 106, 99, 103, 2.9]
    ]

    # 执行合并
    result = merge_multiple_kline_data([data_a, data_b, data_c])
    for row in result:
        print(row)