from apscheduler.schedulers.blocking import BlockingScheduler
from datetime import datetime
import time
import random

def job(index):
    now = datetime.now()
    time.sleep(random.randint(1, 10) / 10)
    print("任务执行", now, index)

scheduler = BlockingScheduler()
scheduler.add_job(job, 'interval', seconds=1, args=[1])
scheduler.add_job(job, 'interval', seconds=1, args=[2])
scheduler.add_job(job, 'interval', seconds=1, args=[3])
scheduler.add_job(job, 'interval', seconds=1, args=[4])
scheduler.add_job(job, 'interval', seconds=1, args=[5])
scheduler.start()