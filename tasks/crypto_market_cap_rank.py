import requests
from datetime import datetime

from database.cyrpto_market_cap import CryptoMarketCap
from database.db import connect_db
from utils import init_logger
from utils.notification import send_message
from apscheduler.schedulers.blocking import BlockingScheduler


class CryptoMarketCapRank:
    def __init__(self, rank_count=50, max_display_changes=20):
        """
        初始化加密货币市值排名类
        :param rank_count: 获取的排名数量，默认50
        :param max_display_changes: 最多显示的变化数量，默认20
        """
        self.coin_list_data = []
        self.logger = init_logger(
            log_file=f"logs/crypto_market_cap/{{time:YYYY-MM-DD}}.log",
            level="INFO",
            console_level="INFO",
        )
        self.rank_count = rank_count
        self.max_display_changes = max_display_changes

    def get_market_cap_data(self):
        res = requests.get(
            "https://pro-api.coinmarketcap.com/v1/cryptocurrency/listings/latest",
            headers={"X-CMC_PRO_API_KEY": "e6c4e018-4d4d-4eab-8b97-ccb1b572f244"},
        )
        data = res.json()
        coin_list = data["data"][: self.rank_count]
        save_data = []
        for index, coin in enumerate(coin_list):
            save_data.append(
                {
                    "symbol": coin["symbol"],
                    "name": coin["name"],
                    "price": coin["quote"]["USD"]["price"],
                    "market_cap": coin["quote"]["USD"]["market_cap"],
                    "rank": index + 1,
                    "volume": coin["quote"]["USD"]["volume_24h"],
                    "volume_change_24h": coin["quote"]["USD"]["volume_change_24h"],
                    "percent_change_1h": coin["quote"]["USD"]["percent_change_1h"],
                    "percent_change_7d": coin["quote"]["USD"]["percent_change_7d"],
                    "percent_change_24h": coin["quote"]["USD"]["percent_change_24h"],
                    "updated_at": datetime.fromisoformat(
                        coin["quote"]["USD"]["last_updated"].replace("Z", "+00:00")
                    ),
                }
            )

        # 将数据赋值给实例变量（注意缩进，应该在 for 循环外部）
        self.coin_list_data = save_data
        self.logger.info(f"获取到 {len(save_data)} 条市值数据")

    def save_data(self):
        """批量保存数据到数据库"""
        self.logger.info(
            f"开始保存数据，当前数据量: {len(self.coin_list_data) if self.coin_list_data else 0}"
        )

        if not self.coin_list_data:
            self.logger.info("❌ 没有数据需要保存")
            return

        connect_db()

        try:
            # 获取旧数据用于比较
            old_data = list(CryptoMarketCap.objects.all())

            # 如果有旧数据，显示变化分析
            if old_data:
                self.logger.info(f"📊 发现 {len(old_data)} 条历史数据，开始分析变化...")
                self.show_diff(old_data)

            # 删除旧数据
            deleted_count = CryptoMarketCap.objects.delete()
            self.logger.info(f"✅ 删除了 {deleted_count} 条旧数据")

            # 方式1：使用 MongoEngine 的批量插入（推荐）
            documents = [CryptoMarketCap(**data) for data in self.coin_list_data]
            result = CryptoMarketCap.objects.insert(documents, load_bulk=False)
            self.logger.info(f"成功批量插入 {len(result)} 条数据")

        except Exception as e:
            self.logger.info(f"MongoEngine 批量插入失败，尝试 PyMongo 方式: {e}")
            try:
                # 方式2：使用 PyMongo 的 insert_many
                collection = CryptoMarketCap._get_collection()
                result = collection.insert_many(self.coin_list_data, ordered=False)
                self.logger.info(
                    f"PyMongo 批量插入成功: {len(result.inserted_ids)} 条数据"
                )

            except Exception as e2:
                self.logger.info(f"PyMongo 批量插入也失败，尝试逐条插入: {e2}")
                # 方式3：逐条插入（最后的备用方案）
                self._fallback_insert()

    def _fallback_insert(self):
        """备用的逐条插入方法"""
        success_count = 0
        error_count = 0

        for data in self.coin_list_data:
            try:
                CryptoMarketCap(**data).save()
                success_count += 1
            except Exception as e:
                error_count += 1
                self.logger.error(f"插入数据失败 {data.get('symbol', 'Unknown')}: {e}")

        self.logger.info(
            f"逐条插入完成: 成功 {success_count} 条，失败 {error_count} 条"
        )

    def show_diff(self, old_data):
        """比较新旧数据的差异，显示市值排名变化"""
        if not old_data or not self.coin_list_data:
            self.logger.warning("❌ 缺少新旧数据，无法进行比较")
            return

        self.logger.info("📊 开始加密货币市值排名变化分析")

        # 构建消息内容
        message_lines = []
        message_lines.append("📊 加密货币市值排名变化分析")
        message_lines.append("=" * 50)

        # 将旧数据转换为字典，便于查找
        old_dict = {}
        for item in old_data:
            # 处理 MongoEngine 文档对象
            if hasattr(item, "to_mongo"):
                data = item.to_mongo().to_dict()
            else:
                data = item
            old_dict[data["symbol"]] = data

        # 将新数据转换为字典
        new_dict = {item["symbol"]: item for item in self.coin_list_data}

        # 分析排名变化
        rank_changes = []
        new_entries = []
        dropped_entries = []

        # 检查新数据中的每个币种
        for symbol, new_data in new_dict.items():
            if symbol in old_dict:
                old_rank = old_dict[symbol]["rank"]
                new_rank = new_data["rank"]
                rank_change = old_rank - new_rank  # 正数表示排名上升

                if rank_change != 0:
                    rank_changes.append(
                        {
                            "symbol": symbol,
                            "name": new_data["name"],
                            "old_rank": old_rank,
                            "new_rank": new_rank,
                            "change": rank_change,
                            "old_price": old_dict[symbol]["price"],
                            "new_price": new_data["price"],
                            "price_change_pct": (
                                (new_data["price"] - old_dict[symbol]["price"])
                                / old_dict[symbol]["price"]
                            )
                            * 100,
                            "old_market_cap": old_dict[symbol]["market_cap"],
                            "new_market_cap": new_data["market_cap"],
                            "market_cap_change_pct": (
                                (
                                    new_data["market_cap"]
                                    - old_dict[symbol]["market_cap"]
                                )
                                / old_dict[symbol]["market_cap"]
                            )
                            * 100,
                        }
                    )
            else:
                # 新进入前n的币种
                new_entries.append(
                    {
                        "symbol": symbol,
                        "name": new_data["name"],
                        "rank": new_data["rank"],
                        "price": new_data["price"],
                        "market_cap": new_data["market_cap"],
                    }
                )

        # 检查从前N掉出的币种
        for symbol, old_data_item in old_dict.items():
            if symbol not in new_dict:
                dropped_entries.append(
                    {
                        "symbol": symbol,
                        "name": old_data_item["name"],
                        "old_rank": old_data_item["rank"],
                        "old_price": old_data_item["price"],
                        "old_market_cap": old_data_item["market_cap"],
                    }
                )

        # 分析排名变化（按变化幅度排序）
        if rank_changes:
            self.logger.info(f"🔄 发现 {len(rank_changes)} 个币种排名变化")
            message_lines.append(f"\n🔄 排名变化 ({len(rank_changes)}个):")
            rank_changes.sort(key=lambda x: abs(x["change"]), reverse=True)

            # 记录详细的排名变化到日志
            for change in rank_changes[: self.max_display_changes]:
                rank_direction = "上升" if change["change"] > 0 else "下降"
                self.logger.info(
                    f"排名变化: {change['symbol']} ({change['name']}) "
                    f"排名{rank_direction} {abs(change['change'])} 位 "
                    f"({change['old_rank']} → {change['new_rank']}), "
                    f"价格变化: {change['price_change_pct']:+.2f}%, "
                    f"市值变化: {change['market_cap_change_pct']:+.2f}%"
                )

            # 添加到消息中（简化版本，只显示前10个）
            for change in rank_changes[: min(10, len(rank_changes))]:
                rank_arrow = "📈" if change["change"] > 0 else "📉"
                price_arrow = "📈" if change["price_change_pct"] > 0 else "📉"

                rank_text = (
                    f"{change['old_rank']}→{change['new_rank']} ({change['change']:+d})"
                )
                price_text = f"{change['price_change_pct']:+.2f}%"

                message_lines.append(
                    f"{rank_arrow} {change['symbol']} {change['name'][:10]} "
                    f"排名: {rank_text}, 价格: {price_arrow}{price_text}"
                )

        # 分析新进入的币种
        if new_entries:
            self.logger.info(
                f"🆕 发现 {len(new_entries)} 个新进入前{self.rank_count}的币种"
            )
            message_lines.append(
                f"\n🆕 新进入前{self.rank_count} ({len(new_entries)}个):"
            )
            for entry in new_entries:
                self.logger.info(
                    f"新进入: {entry['symbol']} ({entry['name']}) "
                    f"排名: #{entry['rank']}, 价格: ${entry['price']:,.2f}, "
                    f"市值: ${entry['market_cap']:,.0f}"
                )
                message_lines.append(
                    f"🆕 {entry['symbol']} {entry['name'][:10]} "
                    f"#{entry['rank']} ${entry['price']:,.2f}"
                )

        # 分析掉出的币种
        if dropped_entries:
            self.logger.info(
                f"📉 发现 {len(dropped_entries)} 个掉出前{self.rank_count}的币种"
            )
            message_lines.append(
                f"\n📉 掉出前{self.rank_count} ({len(dropped_entries)}个):"
            )
            for entry in dropped_entries:
                self.logger.info(
                    f"掉出: {entry['symbol']} ({entry['name']}) "
                    f"原排名: #{entry['old_rank']}, 原价格: ${entry['old_price']:,.2f}, "
                    f"原市值: ${entry['old_market_cap']:,.0f}"
                )
                message_lines.append(
                    f"📉 {entry['symbol']} {entry['name'][:10]} "
                    f"原#{entry['old_rank']} ${entry['old_price']:,.2f}"
                )

        # 统计摘要
        summary_info = {
            "rank_changes": len(rank_changes),
            "new_entries": len(new_entries),
            "dropped_entries": len(dropped_entries),
        }

        self.logger.info(
            f"📈 变化摘要: 排名变化 {summary_info['rank_changes']} 个, "
            f"新进入 {summary_info['new_entries']} 个, "
            f"掉出 {summary_info['dropped_entries']} 个"
        )

        message_lines.append(f"\n📈 变化摘要:")
        message_lines.append(f"• 排名变化: {summary_info['rank_changes']} 个")
        message_lines.append(
            f"• 新进入前{self.rank_count}: {summary_info['new_entries']} 个"
        )
        message_lines.append(
            f"• 掉出前{self.rank_count}: {summary_info['dropped_entries']} 个"
        )

        # 最大涨跌幅
        if rank_changes:
            biggest_gainer = max(rank_changes, key=lambda x: x["price_change_pct"])
            biggest_loser = min(rank_changes, key=lambda x: x["price_change_pct"])

            self.logger.info(
                f"💰 价格变化极值: "
                f"最大涨幅 {biggest_gainer['symbol']} +{biggest_gainer['price_change_pct']:.2f}%, "
                f"最大跌幅 {biggest_loser['symbol']} {biggest_loser['price_change_pct']:.2f}%"
            )

            message_lines.append(f"\n💰 价格变化极值:")
            message_lines.append(
                f"📈 最大涨幅: {biggest_gainer['symbol']} ({biggest_gainer['name'][:8]}) +{biggest_gainer['price_change_pct']:.2f}%"
            )
            message_lines.append(
                f"📉 最大跌幅: {biggest_loser['symbol']} ({biggest_loser['name'][:8]}) {biggest_loser['price_change_pct']:.2f}%"
            )

        # 发送消息通知
        if rank_changes or new_entries or dropped_entries:
            message_content = "\n".join(message_lines)
            self.logger.info("📤 发送市值排名变化通知")
            send_message(message_content, title="加密货币市值排名变化")
        else:
            self.logger.info("📝 无市值排名变化，不发送通知")

        self.logger.info("✅ 市值排名变化分析完成")

    def run(self):
        try:
            self.logger.info("开始运行加密货币市值排名任务")
            self.get_market_cap_data()
            self.save_data()
            self.logger.info("加密货币市值排名任务完成")
        except Exception as e:
            self.logger.error(f"加密货币市值排名任务执行失败: {e}")
            send_message(
                f"加密货币市值排名任务执行失败: {e}", title="加密货币市值排名变化"
            )


if __name__ == "__main__":
    # 创建一个实例，然后依次调用方法
    # 可以自定义排名数量和显示变化数量
    crypto_rank = CryptoMarketCapRank(rank_count=15, max_display_changes=15)
    crypto_rank.run()
    scheduler = BlockingScheduler()
    scheduler.add_job(
        crypto_rank.run, "cron", hour="*", minute=15, id="crypto_rank_job"
    )
    scheduler.start()
