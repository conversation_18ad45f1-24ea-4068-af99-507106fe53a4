# 股票及板块市值
from datetime import datetime
import sys
import os

# 将项目根目录添加到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

import akshare as ak
from apscheduler.schedulers.blocking import BlockingScheduler
from utils import send_message, now, init_logger, is_trade
from database import SectorStocksRank
from database.db import connect_db

logger = init_logger(
    log_file=f"logs/stock_sector_rank/{{time:YYYY-MM-DD}}.log",
    level="INFO",
)


class StockSectorRank:
    def __init__(self, top_sector=30, top_stock=5):
        self.new_data = []
        self.top_sector = top_sector
        self.top_stock = top_stock
        self.is_change = False
        connect_db()

    def get_top_sector(self):
        """
        获取top板块市值数据
        """
        logger.info(f"开始获取前 {self.top_sector} 个板块市值数据")

        sector_df = ak.stock_board_industry_name_em()
        if sector_df.empty:
            logger.error("获取到的行业板块数据为空")
            return []

        # 按总市值排序并获取前N个
        top_sectors_df = sector_df.sort_values(by="总市值", ascending=False).head(
            self.top_sector
        )

        logger.info(f"成功获取 {len(top_sectors_df)} 个板块数据")

        for rank, (_, row) in enumerate(top_sectors_df.iterrows()):
            self.new_data.append(
                {
                    "code": row["板块代码"],
                    "name": row["板块名称"],
                    "rank": rank + 1,
                    "market_value": row["总市值"],
                    "updated_at": now(),
                }
            )

    def get_top_sector_stock(self):
        """
        获取top板块股票数据
        """
        logger.info(f"开始获取各板块前 {self.top_stock} 只股票数据")

        if not self.new_data:
            logger.error("板块数据为空，请先获取板块数据")
            return

        all_stocks_df = ak.stock_zh_a_spot_em()
        if all_stocks_df.empty:
            logger.error("获取A股股票数据失败")
            return

        logger.info(f"成功获取 {len(all_stocks_df)} 只股票数据")

        # 去除总市值为空的行
        all_stocks_df = all_stocks_df.dropna(subset=["总市值"])
        logger.info(f"过滤后剩余 {len(all_stocks_df)} 只有效股票数据")

        # 创建股票代码到股票信息的映射
        stock_info_map = {row["代码"]: row for _, row in all_stocks_df.iterrows()}

        for sector in self.new_data:
            try:
                # 获取该行业板块的成分股
                logger.debug(
                    f"正在获取板块 {sector['name']} ({sector['code']}) 的成分股..."
                )
                sector_stocks_df = ak.stock_board_industry_cons_em(
                    symbol=sector["code"]
                )

                if sector_stocks_df.empty:
                    logger.warning(
                        f"板块 {sector['name']} ({sector['code']}) 成分股数据为空"
                    )
                    sector["top_stocks"] = []
                    continue

                logger.debug(
                    f"板块 {sector['name']} 包含 {len(sector_stocks_df)} 只股票"
                )

                # 获取板块内股票的市值信息
                sector_stocks_with_market_value = []

                for _, row in sector_stocks_df.iterrows():
                    stock_code = row["代码"]
                    if stock_code in stock_info_map:
                        stock_info = stock_info_map[stock_code]
                        sector_stocks_with_market_value.append(
                            {
                                "code": stock_code,
                                "name": stock_info["名称"],
                                "market_value": stock_info["总市值"],
                                "price": stock_info["最新价"],
                                "updated_at": now(),
                            }
                        )

                # 按总市值排序并获取前N个
                sector_stocks_with_market_value.sort(
                    key=lambda x: x["market_value"], reverse=True
                )
                top_stocks = sector_stocks_with_market_value[: self.top_stock]

                # 添加排名
                for i, stock in enumerate(top_stocks):
                    stock["rank"] = i + 1

                sector["top_stocks"] = top_stocks
                logger.debug(f"板块 {sector['name']} 获取到前 {len(top_stocks)} 只股票")

            except Exception as e:
                logger.error(
                    f"获取板块 {sector['name']} ({sector['code']}) 股票数据失败: {e}"
                )
                sector["top_stocks"] = []
                continue

    def show_data_diff(self):
        """比较新旧数据的差异，显示板块排名变化和板块内股票排名变化"""
        logger.info("🔍 开始比较数据差异")

        # 获取旧数据
        old_data = list(SectorStocksRank.objects)
        if not old_data:
            logger.info("📝 数据库中没有旧数据，无法比较差异")
            return

        logger.info(f"📊 从数据库获取到 {len(old_data)} 条历史数据")

        # 数据预处理
        old_data_map = self._build_old_data_map(old_data)
        self._deduplicate_new_data()

        # 分析变化
        sector_changes = self._analyze_sector_changes(old_data_map)
        stock_changes = self._analyze_stock_changes(old_data_map)

        # 发送通知
        self._send_change_notification(sector_changes, stock_changes)

        logger.info("✅ 数据差异比较完成")

    def _build_old_data_map(self, old_data):
        """构建旧数据映射字典"""
        return {item.code: item for item in old_data}

    def _deduplicate_new_data(self):
        """去重处理：确保新数据中没有重复的板块代码"""
        unique_new_data = {}
        for sector in self.new_data:
            sector_code = sector["code"]
            if sector_code not in unique_new_data:
                unique_new_data[sector_code] = sector
            else:
                logger.warning(f"发现重复的板块代码: {sector_code}")

        self.new_data = list(unique_new_data.values())
        logger.info(f"去重后保留 {len(self.new_data)} 个板块")

    def _analyze_sector_changes(self, old_data_map):
        """分析板块排名变化"""
        logger.info("开始分析板块排名变化")

        changes_count = 0
        new_sector_codes = set()
        sector_changes = []

        # 分析现有板块的排名变化
        for new_sector in self.new_data:
            sector_code = new_sector["code"]
            new_sector_codes.add(sector_code)

            if sector_code in old_data_map:
                old_sector = old_data_map[sector_code]
                rank_change = old_sector.rank - new_sector["rank"]

                if rank_change != 0:
                    changes_count += 1
                    change_info = {
                        "type": "rank_change",
                        "sector_code": sector_code,
                        "sector_name": new_sector["name"],
                        "old_rank": old_sector.rank,
                        "new_rank": new_sector["rank"],
                        "rank_change": rank_change,
                        "change_pct": new_sector.get("change_pct", 0),
                    }
                    sector_changes.append(change_info)

                    direction = "上升" if rank_change > 0 else "下降"
                    logger.info(
                        f"板块 {new_sector['name']} 排名{direction}: {old_sector.rank} → {new_sector['rank']} (变化: {rank_change:+d})"
                    )
            else:
                # 新出现的板块
                changes_count += 1
                change_info = {
                    "type": "new_sector",
                    "sector_code": sector_code,
                    "sector_name": new_sector["name"],
                    "new_rank": new_sector["rank"],
                    "change_pct": new_sector.get("change_pct", 0),
                }
                sector_changes.append(change_info)
                logger.info(
                    f"新板块出现: {new_sector['name']} (排名: {new_sector['rank']})"
                )

        # 检查消失的板块
        removed_sectors = 0
        for old_sector_code, old_sector in old_data_map.items():
            if old_sector_code not in new_sector_codes:
                removed_sectors += 1
                changes_count += 1
                change_info = {
                    "type": "removed_sector",
                    "sector_code": old_sector_code,
                    "sector_name": old_sector.name,
                    "old_rank": old_sector.rank,
                }
                sector_changes.append(change_info)
                logger.info(f"板块消失: {old_sector.name} (原排名: {old_sector.rank})")

        logger.info(
            f"板块变化统计: 总变化 {changes_count} 个，消失 {removed_sectors} 个"
        )
        return {
            "changes": sector_changes,
            "total_changes": changes_count,
            "removed_count": removed_sectors,
        }

    def _analyze_stock_changes(self, old_data_map):
        """分析板块内股票排名变化"""
        logger.info("开始分析股票排名变化")

        stock_changes = []

        for new_sector in self.new_data:
            sector_code = new_sector["code"]
            if sector_code in old_data_map:
                old_sector = old_data_map[sector_code]
                sector_stock_changes = self._compare_sector_stocks(
                    new_sector, old_sector
                )
                if sector_stock_changes:
                    stock_changes.extend(sector_stock_changes)

        logger.info(f"股票变化统计: 总变化 {len(stock_changes)} 个")
        return stock_changes

    def _compare_sector_stocks(self, new_sector, old_sector):
        """比较单个板块内的股票变化"""
        sector_changes = []

        # 创建旧股票数据的映射字典
        old_stocks_map = {stock.code: stock for stock in old_sector.top_stocks}

        # 比较每只股票的排名变化
        for new_stock in new_sector["top_stocks"]:
            stock_code = new_stock["code"]

            if stock_code in old_stocks_map:
                old_stock = old_stocks_map[stock_code]
                rank_change = old_stock.rank - new_stock["rank"]

                if rank_change != 0:
                    change_info = {
                        "type": "stock_rank_change",
                        "sector_name": new_sector["name"],
                        "stock_code": stock_code,
                        "stock_name": new_stock["name"],
                        "old_rank": old_stock.rank,
                        "new_rank": new_stock["rank"],
                        "rank_change": rank_change,
                    }
                    sector_changes.append(change_info)
            else:
                # 新进入排名的股票
                change_info = {
                    "type": "new_stock",
                    "sector_name": new_sector["name"],
                    "stock_code": stock_code,
                    "stock_name": new_stock["name"],
                    "new_rank": new_stock["rank"],
                }
                sector_changes.append(change_info)

        # 检查从排名中消失的股票
        new_stock_codes = {stock["code"] for stock in new_sector["top_stocks"]}
        for old_stock_code, old_stock in old_stocks_map.items():
            if old_stock_code not in new_stock_codes:
                change_info = {
                    "type": "removed_stock",
                    "sector_name": new_sector["name"],
                    "stock_code": old_stock_code,
                    "stock_name": old_stock.name,
                    "old_rank": old_stock.rank,
                }
                sector_changes.append(change_info)

        return sector_changes

    def _send_change_notification(self, sector_changes, stock_changes):
        """发送变化通知"""
        logger.info("开始构建通知内容")

        # 格式化板块变化
        formatted_sector_changes = self._format_sector_changes(sector_changes)
        formatted_stock_changes = self._format_stock_changes(stock_changes)

        # 构建通知内容
        if formatted_sector_changes or formatted_stock_changes:
            self.is_change = True

            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            notification = f"📊 股票板块排名变化通知 ({current_time})\n\n"

            if formatted_sector_changes:
                notification += f"📈 板块变化 ({len(formatted_sector_changes)}项):\n"
                notification += "\n".join(formatted_sector_changes)
                notification += "\n\n"

            if formatted_stock_changes:
                notification += f"📊 股票变化 ({len(formatted_stock_changes)}项):\n"
                notification += "\n".join(formatted_stock_changes)

            logger.info("📤 发送变化通知")
            send_message(notification, title="股票板块排名变化")
        else:
            logger.info("📝 无变化，不发送通知")

    def _format_sector_changes(self, sector_changes):
        """格式化板块变化信息"""
        formatted = []
        for change in sector_changes["changes"]:
            if change["type"] == "rank_change":
                direction = "上升" if change["rank_change"] > 0 else "下降"
                icon = "🔼" if change["rank_change"] > 0 else "🔽"
                text = f"{change['sector_name']} ({change['sector_code']}) 板块排名{direction}了 {abs(change['rank_change'])} 位（{change['old_rank']} -> {change['new_rank']}）"
                formatted.append(f"{icon} {text}")
            elif change["type"] == "new_sector":
                formatted.append(
                    f"🆕 新增板块: {change['sector_name']} ({change['sector_code']})，当前排名: {change['new_rank']}"
                )
            elif change["type"] == "removed_sector":
                formatted.append(
                    f"❌ {change['sector_name']} ({change['sector_code']}) 板块已从排名中移除，原排名: {change['old_rank']}"
                )
        return formatted

    def _format_stock_changes(self, stock_changes):
        """格式化股票变化信息"""
        formatted = []
        for change in stock_changes:
            if change["type"] == "stock_rank_change":
                direction = "上升" if change["rank_change"] > 0 else "下降"
                icon = "🔼" if change["rank_change"] > 0 else "🔽"
                text = f"{change['sector_name']} 板块中的股票 {change['stock_name']} ({change['stock_code']}) 排名{direction}了 {abs(change['rank_change'])} 位（{change['old_rank']} -> {change['new_rank']}）"
                formatted.append(f"{icon} {text}")
            elif change["type"] == "new_stock":
                formatted.append(
                    f"🆕 {change['sector_name']} 板块中新增股票: {change['stock_name']} ({change['stock_code']})，当前排名: {change['new_rank']}"
                )
            elif change["type"] == "removed_stock":
                formatted.append(
                    f"❌ {change['sector_name']} 板块中的股票 {change['stock_name']} ({change['stock_code']}) 已从排名中移除"
                )
        return formatted

    def save_data(self):
        if not self.is_change:
            logger.info("数据无变化，不保存")
            return
        logger.info("开始保存数据到数据库")

        try:
            # 先删除旧数据
            SectorStocksRank.objects.delete()
            logger.info("已删除旧数据")

            # 插入新数据（已在show_data_diff中去重）
            doc_data = [SectorStocksRank(**sector) for sector in self.new_data]
            SectorStocksRank.objects.insert(doc_data)
            logger.info(f"成功保存 {len(doc_data)} 条新数据")
        except Exception as e:
            logger.error(f"保存数据失败: {e}")
            raise

    def run(self):
        if not is_trade():
            logger.debug("非交易日，不运行股票板块排名任务")
            return
        try:
            logger.info("开始运行股票板块排名任务")
            self.get_top_sector()
            self.get_top_sector_stock()
            self.show_data_diff()
            self.save_data()
            logger.info("股票板块排名任务完成")
        except Exception as e:
            logger.error(f"股票板块排名任务执行失败: {e}")
            send_message(
                f"【任务失败】股票板块排名任务\n执行时间: {now()}\n错误信息: {str(e)}",
                "股票板块排名任务",
            )
            raise e


def run_stock_sector_rank():
    """运行股票板块排名任务的包装函数"""
    try:
        task = StockSectorRank()
        task.run()
    except Exception as e:
        logger.error(f"股票板块排名任务执行失败: {e}")


if __name__ == "__main__":
    # 立即执行一次任务
    logger.info("🚀 立即执行股票板块排名任务")
    run_stock_sector_rank()

    # 设置定时任务
    scheduler = BlockingScheduler()
    scheduler.add_job(
        run_stock_sector_rank,  # 使用函数而不是实例方法
        "cron",
        hour=15,
        minute=20,
        misfire_grace_time=5 * 60,
        coalesce=True,
        id="stock_sector_rank_job",  # 添加任务ID
        name="股票板块排名任务",  # 添加任务名称
    )

    logger.info("📅 定时任务已设置：每天15:20执行")
    scheduler.start()
