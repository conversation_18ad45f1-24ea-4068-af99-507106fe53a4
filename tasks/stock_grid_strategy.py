from datetime import datetime
import os
import sys
from typing import Optional

import pymongo


# 将项目根目录添加到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)
from database.stock_transaction import StockTransaction
from utils.notification import send_message, send_message_with_link
from database.db import connect_db
from database.stock_strategy import StockStrategy, StockStrategyHistory
import akshare as ak
import pandas as pd
from utils import init_logger, to_fixed, is_trade, now, timing_decorator
from apscheduler.schedulers.blocking import BlockingScheduler
from service.gird_strategy import get_grid_data, get_stock_amount

logger = init_logger(
    log_file=f"logs/stock_grid_strategy/{{time:YYYY-MM-DD}}.log",
    level="INFO",
)


class StockGridStrategy:
    """
    网格交易策略
    """

    def __init__(self):
        self.stock_list = []
        # 未来转移到网站配置中
        self.notification_interval = 60 * 5
        self.notify_time = datetime.now().timestamp()

    def notify(
        self,
        message: str,
        link_text: Optional[str] = None,
        link_url: Optional[str] = None,
    ):
        """
        发送通知
        """
        if datetime.now().timestamp() - self.notify_time < self.notification_interval:
            return
        if link_text and link_url:
            send_message_with_link(
                msg=message,
                link_text=link_text,
                link_url=link_url,
                title="股票网格交易策略",
            )
        else:
            send_message(message, "股票网格交易策略")
        self.notify_time = datetime.now().timestamp()

    @logger.timing_decorator("获取股票信息")
    def get_stock_info(self):
        """
        获取数据库股票信息
        获取股票市场信息
        """
        connect_db()
        logger.info("已连接数据库")

        self.stock_list = StockStrategy.objects().all()
        logger.info(f"从数据库获取到 {len(self.stock_list)} 只股票")

    def update_target_price(
        self,
        stock_position: StockStrategy,
        force_update: bool = False,
    ):
        """
        更新网格交易的目标价格

        参数:
            stock_position: 股票持仓信息
            grid_strategy: 网格交易策略
            force_update: 是否强制更新目标价格
        """
        grid_strategy = stock_position.grid_strategy

        # 获取当前目标价格
        current_target_price = getattr(grid_strategy, "target_price", None)

        # 如果没有目标价格，需要初始化设置
        if not current_target_price:
            # 优先使用入场价格，如果没有则使用持仓价格
            base_price = (
                getattr(grid_strategy, "entry_price", None)
                or stock_position.position_price
            )
            grid_strategy.target_price = base_price * (1 + grid_strategy.grid_size)
            logger.info(
                f"初始化目标价格: {stock_position.name}({stock_position.code}) 目标价: {grid_strategy.target_price:.2f}"
            )
            return grid_strategy.target_price

        # 如果需要强制更新目标价格
        if force_update:
            old_target_price = grid_strategy.target_price
            grid_strategy.target_price = current_target_price * (
                1 + grid_strategy.grid_size
            )
            logger.info(
                f"强制更新目标价格: {stock_position.name}({stock_position.code}) 旧目标价: {old_target_price:.2f} -> 新目标价: {grid_strategy.target_price:.2f}"
            )
            return grid_strategy.target_price

    @logger.timing_decorator("设置股票市场信息", threshold_seconds=5)
    def set_stock_market_info(self):
        """
        设置股票市场信息
        """

        for stock in self.stock_list:
            try:
                stock_info = ak.stock_bid_ask_em(symbol=stock.code)
                # 设置 "item" 列为索引
                stock_info.set_index("item", inplace=True)
                stock_info = stock_info.T
                if stock_info.empty or getattr(stock, "grid_strategy", None) is None:
                    continue

                stock.price = stock_info["最新"].iloc[0]
                stock.pct_change = stock_info["涨幅"].iloc[0]
                stock.position_value = to_fixed(stock.position_amount * stock.price)
                stock.unrealized_pnl = to_fixed(
                    stock.position_value - stock.position_cost_value
                )
                stock.updated_at = now()
                self.update_target_price(stock)
                self.update_grid_strategy_info(stock)
                self.check_grid_price_trigger(stock)
            except Exception as e:
                logger.error(f"获取股票 {stock.code} {stock.name} 信息失败: {e}")

    @logger.timing_decorator("保存数据到数据库")
    def save_db(self):
        """
        保存数据到数据库
        """
        bulk_operations = []

        for stock in self.stock_list:
            # 准备更新操作
            update_data = stock.to_mongo().to_dict()

            # 创建更新操作
            bulk_operations.append(
                pymongo.UpdateOne({"code": stock.code}, {"$set": update_data})
            )

        if bulk_operations:
            result = StockStrategy._get_collection().bulk_write(bulk_operations)
            logger.info(
                f"数据库批量更新完成: 匹配 {result.matched_count} 条, 修改 {result.modified_count} 条"
            )

    def check_grid_price_trigger(self, position: StockStrategy):
        """
        检查网格交易价格触发
        """
        buy_price = position.grid_strategy.buy_price
        sell_price = position.grid_strategy.sell_price
        buy_amount = position.grid_strategy.buy_amount
        sell_amount = position.grid_strategy.sell_amount
        price = position.price
        trigger_percent = 0.02
        position_amount = position.position_amount

        if price <= buy_price * (1 + trigger_percent):
            logger.info(
                f"触发买入信号: {position.name}({position.code}) 当前价: {price:.2f} 买入价: {buy_price:.2f}"
            )
            self.notify(
                f"【买入提醒】{position.name}({position.code})\n"
                f"当前价: ¥{price:.2f} 接近买入价: ¥{buy_price:.2f}\n"
                f"建议买入: {buy_amount}股",
                link_text="更新交易记录",
                link_url=f"https://investflow.yc123h.fun/dashboard/stock/transactions",
            )
        elif price >= sell_price * (1 - trigger_percent):
            if position_amount < sell_amount * 2:
                logger.info(
                    f"持仓不足两个网格，更新目标价格: {position.name}({position.code})"
                )
                new_target_price = self.update_target_price(position, True)
                new_invest_asset = self.update_invest_asset(position)
                self.notify(
                    f"【目标价更新】{position.name}({position.code})\n"
                    f"持仓量不足两个网格，已自动调整\n"
                    f"新目标价: ¥{new_target_price:.2f}\n"
                    f"新投资资产: ¥{new_invest_asset:.2f}"
                )
            else:
                logger.info(
                    f"触发卖出信号: {position.name}({position.code}) 当前价: {price:.2f} 卖出价: {sell_price:.2f}"
                )
                self.notify(
                    f"【卖出提醒】{position.name}({position.code})\n"
                    f"当前价: ¥{price:.2f} 接近卖出价: ¥{sell_price:.2f}\n"
                    f"建议卖出: {sell_amount}股",
                    link_text="更新交易记录",
                    link_url=f"https://investflow.yc123h.fun/dashboard/stock/transactions",
                )

    def update_invest_asset(self, stock: StockStrategy):
        """
        更新投资资产
        (全部交易盈亏+未实现盈亏+投资总额) / 股票数量
        """
        stock_transactions = StockTransaction.objects()
        # 所有股票的总交易盈亏
        total_pnl = sum(
            stock_transaction.pnl for stock_transaction in stock_transactions
        )
        # 所有股票的总未实现盈亏
        total_unrealized_pnl = sum(stock.unrealized_pnl for stock in self.stock_list)
        # 所有股票的投资总额
        total_invest_asset = sum(
            stock.grid_strategy.invest_asset for stock in self.stock_list
        )
        # 新的投资资产
        invest_asset = (total_pnl + total_unrealized_pnl + total_invest_asset) / len(
            self.stock_list
        )
        new_invest_asset = max(invest_asset, stock.grid_strategy.invest_asset)
        
        # 更新所有股票的投资资产
        for stock in self.stock_list:
            stock.grid_strategy.invest_asset = to_fixed(new_invest_asset, 0)
        return new_invest_asset

    def update_grid_strategy_info(self, stock: StockStrategy):
        """
        更新网格交易策略信息
        """
        grid_strategy = stock.grid_strategy
        grid_data = get_grid_data(
            invest_asset=grid_strategy.invest_asset,
            leverage=grid_strategy.leverage,
            grid_size=grid_strategy.grid_size,
            target_price=grid_strategy.target_price,
            position_amount=stock.position_amount,
        )
        grid_strategy.buy_price = to_fixed(grid_data["buy_price"], 2)
        grid_strategy.sell_price = to_fixed(grid_data["sell_price"], 2)
        grid_strategy.buy_amount = get_stock_amount(grid_data["buy_amount"])
        grid_strategy.sell_amount = get_stock_amount(grid_data["sell_amount"])
        grid_strategy.grid_value = to_fixed(grid_data["trade_value"], 0)

    @logger.timing_decorator("运行股票网格交易策略")
    def run(self, force_update=False):
        """
        运行股票网格交易策略
        """
        try:
            if not is_trade(check_time=True) and not force_update:
                logger.debug("非交易时间，不运行股票网格策略监控任务")
                return
            logger.info("开始运行股票策略任务")

            self.get_stock_info()
            self.set_stock_market_info()
            self.save_db()

            logger.info("股票策略任务执行完成")
        except Exception as e:
            logger.error(f"股票策略任务执行失败: {e}")
            # 可以在这里添加通知机制，比如发送邮件或消息
            send_message(
                f"【任务失败】股票网格交易策略\n执行时间: {now()}\n错误信息: {str(e)}",
                "股票网格交易策略",
            )

    def add_history(self):
        """
        添加股票策略历史
        """
        history_records = []
        for stock in self.stock_list:
            # 将对象转换为字典，添加created_at字段
            data = stock.to_mongo().to_dict()
            # 移除_id字段，让MongoDB自动生成新的ID
            if "_id" in data:
                del data["_id"]
            data["created_at"] = now()
            # 创建新的历史记录对象
            history = StockStrategyHistory(**data)
            history_records.append(history)

        if history_records:
            StockStrategyHistory.objects.insert(history_records)
            logger.info(f"数据库批量插入完成: 插入 {len(history_records)} 条记录")

    def generate_summary(self):

        # 计算投资组合总体情况
        total_position_value = 0  # 总持仓市值
        total_position_cost = 0  # 总持仓成本
        total_unrealized_pnl = 0  # 总未实现盈亏

        # 按盈亏百分比排序的股票列表
        sorted_stocks = []
        # 按涨跌幅排序的股票列表
        pct_change_stocks = []

        for stock in self.stock_list:
            # 计算盈亏百分比
            pnl_percent = 0
            if stock.position_cost_value > 0:
                pnl_percent = (stock.unrealized_pnl / stock.position_cost_value) * 100

            # 添加到排序列表
            stock_info = {
                "code": stock.code,
                "name": stock.name,
                "position_amount": stock.position_amount,
                "position_cost_value": stock.position_cost_value,
                "position_value": stock.position_value,
                "unrealized_pnl": stock.unrealized_pnl,
                "pnl_percent": pnl_percent,
                "pct_change": stock.pct_change if stock.pct_change is not None else 0,
            }

            sorted_stocks.append(stock_info)
            pct_change_stocks.append(stock_info)

            # 累计总数据
            total_position_value += stock.position_value
            total_position_cost += stock.position_cost_value
            total_unrealized_pnl += stock.unrealized_pnl

        # 按盈亏金额降序排序
        sorted_stocks.sort(key=lambda x: x["unrealized_pnl"], reverse=True)
        # 按涨跌幅降序排序
        pct_change_stocks.sort(key=lambda x: x["pct_change"], reverse=True)

        # 计算总体盈亏百分比
        total_pnl_percent = 0
        if total_position_cost > 0:
            total_pnl_percent = (total_unrealized_pnl / total_position_cost) * 100

        # 构建消息
        summary_info = "📊 **投资组合总览**\n"
        summary_info += f"总持仓市值: ¥{total_position_value:.2f}\n"
        summary_info += f"总持仓成本: ¥{total_position_cost:.2f}\n"
        summary_info += (
            f"总未实现盈亏: ¥{total_unrealized_pnl:.2f} ({total_pnl_percent:.2f}%)\n\n"
        )

        # 添加表现最好的股票（如果有）
        if len(sorted_stocks) > 0:
            best_stock = sorted_stocks[0]
            summary_info += (
                f"🔝 **收益最佳**: {best_stock['name']}({best_stock['code']}) "
            )
            summary_info += f"{best_stock['pnl_percent']:.2f}% (¥{best_stock['unrealized_pnl']:.2f})\n"

        # 添加表现最差的股票（如果有）
        if len(sorted_stocks) > 1:
            worst_stock = sorted_stocks[-1]
            summary_info += (
                f"⚠️ **收益最差**: {worst_stock['name']}({worst_stock['code']}) "
            )
            summary_info += f"{worst_stock['pnl_percent']:.2f}% (¥{worst_stock['unrealized_pnl']:.2f})\n\n"

        # 添加当日涨幅最大的股票（如果有）
        if len(pct_change_stocks) > 0:
            best_pct_stock = pct_change_stocks[0]
            summary_info += f"📈 **今日涨幅最大**: {best_pct_stock['name']}({best_pct_stock['code']}) "
            summary_info += f"{best_pct_stock['pct_change']:.2f}%\n"

        # 添加当日跌幅最大的股票（如果有）
        if len(pct_change_stocks) > 1:
            worst_pct_stock = pct_change_stocks[-1]
            summary_info += f"📉 **今日跌幅最大**: {worst_pct_stock['name']}({worst_pct_stock['code']}) "
            summary_info += f"{worst_pct_stock['pct_change']:.2f}%\n\n"

        # 添加股票明细表格（如果股票数量超过5个，只显示前3名和后2名）
        summary_info += "� **股票明细**\n"
        summary_info += "股票 | 市值 | 盈亏金额 | 盈亏比例 | 今日涨跌\n"
        summary_info += "--- | --- | --- | --- | ---\n"

        # 决定显示哪些股票
        display_stocks = sorted_stocks
        if len(sorted_stocks) > 5:
            # 只显示前3名和后2名
            display_stocks = sorted_stocks[:3] + sorted_stocks[-2:]

        # 添加股票明细
        for stock in display_stocks:
            summary_info += f"{stock['name']} | ¥{stock['position_value']:.2f} | "
            summary_info += f"¥{stock['unrealized_pnl']:.2f} | {stock['pnl_percent']:.2f}% | {stock['pct_change']:.2f}%\n"

        # 如果有省略的股票，添加提示
        if len(sorted_stocks) > 5:
            summary_info += f"...(省略 {len(sorted_stocks) - 5} 只股票)...\n"

        send_message(summary_info, "股票网格交易策略")
        

    def trade_summary(self):
        """
        交易总结 - 提供简洁明了的投资组合概览
        """
        if not is_trade():
            logger.debug("非交易日，不执行交易总结")
            return
        try:
            self.run(True)  # 注意：原代码中使用的是小写的true，这里改为正确的True
            self.add_history()
            self.generate_summary()
        except Exception as e:
            logger.error(f"交易总结失败: {e}")
            send_message(
                f"【任务失败】股票网格交易策略\n执行时间: {now()}\n错误信息: {str(e)}",
                "股票网格交易策略",
            )
            raise e



if __name__ == "__main__":
    stock_grid_strategy = StockGridStrategy()
    scheduler = BlockingScheduler()
    stock_grid_strategy.run(force_update=True)
    # stock_grid_strategy.trade_summary()
    # 添加任务，并设置错误处理
    scheduler.add_job(
        stock_grid_strategy.run,
        "interval",
        seconds=60,
        misfire_grace_time=5 * 60,  # 错过执行时间后的宽限期（秒）
        coalesce=True,  # 如果错过了多次执行，只执行一次
    )
    # 每天收盘后运行
    scheduler.add_job(
        stock_grid_strategy.trade_summary,
        "cron",
        hour=15,
        minute=1,
        misfire_grace_time=5 * 60,  # 错过执行时间后的宽限期（秒）
        coalesce=True,  # 如果错过了多次执行，只执行一次
    )

    try:
        logger.info("启动定时任务调度器")
        scheduler.start()
    except (KeyboardInterrupt, SystemExit):
        logger.info("定时任务调度器已停止")
