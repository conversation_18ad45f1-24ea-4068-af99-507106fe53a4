from datetime import datetime
import json
import os
import sys
import time

from utils.redis_client import RedisSentinelClient


# 将项目根目录添加到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from utils.exchange import (
    asset_transfer,
    fetch_balance,
    get_earn_flexible_list,
    get_earn_flexible_position,
    post_earn_flexible_redeem,
    post_simple_earn_flexible_subscribe,
)
from utils.common import is_crypto_blocked_transfer_time, to_fixed
from utils.constant import (
    TransferType,
    get_earn_flexible_key,
    exchange_proxy,
)
from utils.notification import send_message
from typing import Any, Dict, List
from database.db import connect_db
from database.exchange_keys import ExchangeKeys
from utils.cipher import decrypt_data
from utils import init_logger
import ccxt
from apscheduler.schedulers.blocking import BlockingScheduler


class CryptoBalanceRobot:
    """
    账户余额机器人，用来调度闲置资金进入到理财账户中
    """

    def __init__(self, exchange_key: ExchangeKeys):
        self.redis = RedisSentinelClient.getInstance()
        self.exchange_key = exchange_key
        self.exchange = None
        self.logger = None
        self.earn_flexible_key = None
        self.earn_flexible_list: List[Dict[str, Any]] = []
        # 设置保证金杠杆倍数，例如杠杆为 5，则预留 100 / 5 的保证金，100 为设置的账户交易杠杆
        self.leverage = 5
        self.aggregation_time = None
        # API 调用间隔
        self.api_sleep = 5

        self._init()

    def _init(self):
        self.logger = init_logger(
            log_file=f"logs/crypto_balance/{self.exchange_key.account}_{self.exchange_key.exchange}/{{time:YYYY-MM-DD}}.log",
            level="INFO",
            console_level="INFO",
            prefix=f"{self.exchange_key.exchange}_{self.exchange_key.account}",
        )
        self._set_exchange()
        self._init_redis_key()
        self.logger.info(
            f"初始化账户余额机器人: {self.exchange_key.exchange}_{self.exchange_key.account}"
        )
        self.logger.info(f"设置保证金杠杆倍数: {self.leverage}")

    def _init_redis_key(self):
        self.earn_flexible_key = get_earn_flexible_key(self.exchange_key.exchange)
        self.lock_key = (
            f"investTask:crypto:{self.exchange_key.account}:balanceController:lock"
        )

    def _set_exchange(self):
        config = {
            "apiKey": self.exchange_key.api_key,
            "secret": decrypt_data(self.exchange_key.api_secret),
            "enableRateLimit": True,
            "proxies": {
                "http": exchange_proxy,
                "https": exchange_proxy,
            },
        }
        self.spot_exchange = ccxt.binance(config)

        self.exchange = ccxt.binance({**config, "options": {"defaultType": "future"}})

    def set_earn_flexible_list(self):
        """
        获活期理财列表
        如果Redis中有有效期内（1小时）的数据，则使用缓存数据
        否则从交易所API获取并更新缓存
        """
        try:
            # 尝试从Redis获取缓存数据
            cached_data = self.redis.get(self.earn_flexible_key)
            if cached_data:
                cached_data = json.loads(cached_data)
                update_at = cached_data.get("update_at", 0)

                # 检查缓存是否在有效期内（1小时）
                cache_valid = datetime.now().timestamp() - update_at < 60 * 60

                if cache_valid and "data" in cached_data:
                    self.logger.info(
                        f"使用缓存活期理财列表数据，更新时间: {datetime.fromtimestamp(update_at)}"
                    )
                    self.earn_flexible_list = cached_data["data"]
                    return

            # 缓存无效或不存在，从API获取新数据
            self.logger.info("从交易所API获活期理财列表")
            self.earn_flexible_list = get_earn_flexible_list(self.exchange)

            if not self.earn_flexible_list:
                self.logger.error("获取活期理财列表失败")
                send_message(
                    f"【任务失败】活期理财列表\n账号信息: {self.exchange_key.exchange}_{self.exchange_key.account}\n执行时间: {datetime.now()}\n错误信息: 获取活期理财列表失败",
                )
                return

            # 更新Redis缓存
            cache_data = {
                "update_at": datetime.now().timestamp(),
                "data": self.earn_flexible_list,
            }
            self.redis.set(self.earn_flexible_key, json.dumps(cache_data))
            self.logger.info(
                f"成功更活期理财列表缓存，共 {len(self.earn_flexible_list)} 条记录"
            )

        except Exception as e:
            self.logger.error(f"获活期理财列表异常: {str(e)}")
            self.earn_flexible_list = []
            send_message(
                f"【任务异常】活期理财列表\n账号信息: {self.exchange_key.exchange}_{self.exchange_key.account}\n执行时间: {datetime.now()}\n错误信息: {str(e)}",
            )

    def _get_idle_balance(self):
        """
        获取账户闲置的USDT余额
        """
        self.logger.info("开始获取账户闲置USDT余额")
        try:
            balance = fetch_balance(self.exchange)
            usdt_balance = balance["USDT"]
            # 预留保证金倍数
            margin_multiple = 100 / self.leverage
            # 需要的 usdt 保证金
            need_margin = usdt_balance["used"] * margin_multiple
            # 除去保证金之后可用于购买理财的 USDT
            available = usdt_balance["free"] - need_margin

            result = {
                "USDT": {
                    **usdt_balance,
                    "need_margin": to_fixed(need_margin, 1),
                    "available": to_fixed(available, 1),
                }
            }

            self.logger.info(
                f"账户余额信息: 总额={usdt_balance['total']}, 已用={usdt_balance['used']}, "
                f"可用={usdt_balance['free']}, 需要保证金={to_fixed(need_margin, 1)}, "
                f"可用于理财={to_fixed(available, 1)}"
            )

            return result
        except Exception as e:
            self.logger.error(f"获取账户余额异常: {str(e)}")
            raise

    def _should_buy_or_redeem(self, balance):
        """
        判断是否应该购买或赎回理财
        """
        self.logger.info("开始判断是否应该购买或赎回理财")
        USDT_balance = balance["USDT"]
        if USDT_balance["used"] == 0:
            # used 为 0，表示没有持仓，可能处在交易中还未成交，不进行理财操作
            self.logger.info("账户没有持仓，不进行理财操作")
            return

        try:
            earn_flexible_positions = get_earn_flexible_position(self.exchange)

            earn_position = [p for p in earn_flexible_positions if p["asset"] == "USDT"]
            earn_product = [p for p in self.earn_flexible_list if p["asset"] == "USDT"]

            # 可用资金小于 0，说明保证金不足，赎回理财
            if USDT_balance["available"] < 0:
                if not earn_position:
                    self.logger.info("保证金不足但没有理财持仓，无法赎回")
                    return
                self.logger.info(
                    f"保证金不足，需要赎回理财: {abs(USDT_balance['available'])}"
                )
                return self._handle_redeem(
                    USDT_balance, earn_position[0], earn_product[0]
                )

            # 可用资金大于 0，且有理财产品，购买理财
            if USDT_balance["available"] > 0 and earn_product:
                self.logger.info(f"有闲置资金，可购买理财: {USDT_balance['available']}")
                return self._handle_buy(USDT_balance, earn_position[0], earn_product[0])

            self.logger.info("当前无需进行理财操作")
        except Exception as e:
            self.logger.error(f"判断理财操作异常: {str(e)}")

    def _get_min_transfer_amount(self, balance, earn_position, earn_product):
        """
        获取最小用户购买理财或者赎回转账金额，以总资产 1% 为最小值
        """
        total = balance["total"] + float(earn_position["totalAmount"])
        min_amount = to_fixed(
            max(total * 0.01, float(earn_product["minPurchaseAmount"])), 1
        )
        self.logger.debug(
            f"最小转账金额: {min_amount}, 总资产: {total}, 最小购买金额: {earn_product['minPurchaseAmount']}"
        )
        return min_amount

    def _handle_buy(self, balance, earn_position, earn_product):
        """
        购买理财
        """
        self.logger.info(f"开始处理购买理财，可用金额: {balance['available']}")
        try:
            min_transfer_amount = self._get_min_transfer_amount(
                balance, earn_position, earn_product
            )
            min_buy_amount = float(earn_product["minPurchaseAmount"])

            if (
                balance["available"] < min_transfer_amount
                or balance["available"] < min_buy_amount
            ):
                self.logger.info(
                    f"可用金额 {balance['available']} 小于最小转账金额 {min_transfer_amount} "
                    f"或最小购买金额 {min_buy_amount}，不进行购买"
                )
                return

            self.logger.info(f"从合约账户划转 {balance['available']} USDT 到现货账户")
            transfer_result = asset_transfer(
                self.exchange,
                {
                    "type": TransferType.USDT_FUTURE_TO_MAIN.value,
                    "asset": "USDT",
                    "amount": str(balance["available"]),
                },
            )
            self.logger.info(f"划转结果: {transfer_result}")
            time.sleep(self.api_sleep)

            self.logger.info(
                f"购买理财产品 {earn_product['productId']}，金额: {balance['available']}"
            )
            subscribe_result = post_simple_earn_flexible_subscribe(
                self.exchange,
                {
                    "productId": earn_product["productId"],
                    "amount": str(balance["available"]),
                },
            )
            self.logger.info(f"购买理财结果: {subscribe_result}")
            time.sleep(self.api_sleep)
            send_message(
                f"【任务成功】购买理财\n账号信息: {self.exchange_key.exchange}_{self.exchange_key.account}\n执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n购买金额: {balance['available']}"
            )
        except Exception as e:
            self.logger.error(f"购买理财异常: {str(e)}")
            send_message(
                f"【任务异常】购买理财\n账号信息: {self.exchange_key.exchange}_{self.exchange_key.account}\n执行时间: {datetime.now()}\n错误信息: {str(e)}"
            )

    def _handle_redeem(self, balance, earn_position, earn_product):
        """
        赎回理财
        """
        self.logger.info(f"开始处理赎回理财，需要赎回金额: {abs(balance['available'])}")
        try:
            redeem_amount = min(
                abs(balance["available"]), float(earn_position["totalAmount"])
            )
            min_transfer_amount = self._get_min_transfer_amount(
                balance, earn_position, earn_product
            )

            if redeem_amount < min_transfer_amount:
                self.logger.info(
                    f"赎回金额 {redeem_amount} 小于最小转账金额 {min_transfer_amount}，不进行赎回"
                )
                return

            self.logger.info(
                f"赎回理财产品 {earn_position['productId']}，金额: {redeem_amount}"
            )
            redeem_result = post_earn_flexible_redeem(
                self.exchange,
                {
                    "productId": earn_position["productId"],
                    "amount": redeem_amount,
                },
            )
            self.logger.info(f"赎回理财结果: {redeem_result}")
            time.sleep(self.api_sleep)

            self.logger.info(f"从现货账户划转 {redeem_amount} USDT 到合约账户")
            transfer_result = asset_transfer(
                self.exchange,
                {
                    "type": TransferType.MAIN_TO_USDT_FUTURE.value,
                    "asset": "USDT",
                    "amount": str(redeem_amount),
                },
            )
            self.logger.info(f"划转结果: {transfer_result}")
            time.sleep(self.api_sleep)
            send_message(
                f"【任务成功】赎回理财\n账号信息: {self.exchange_key.exchange}_{self.exchange_key.account}\n执行时间: {datetime.now()}\n赎回金额: {redeem_amount}"
            )
        except Exception as e:
            send_message(
                f"【任务异常】赎回理财\n账号信息: {self.exchange_key.exchange}_{self.exchange_key.account}\n执行时间: {datetime.now()}\n错误信息: {str(e)}",
            )
            self.logger.error(f"赎回理财异常: {str(e)}")

    def _asset_aggregation(self):
        """
        资产归集，资金进行归集防止有资金划转失败
        """
        if (
            self.aggregation_time
            and (datetime.now() - self.aggregation_time).total_seconds() < 60
        ):
            self.logger.info("资产归集时间间隔未到，跳过")
            return

        self.aggregation_time = datetime.now()

        self.logger.info("开始进行资产归集")
        try:
            balance = fetch_balance(self.spot_exchange)
            usdt_balance = balance.get("USDT")

            if usdt_balance and usdt_balance["free"] > 0:
                self.logger.info(
                    f"现货账户有 {usdt_balance['free']} USDT 可用余额，进行归集"
                )
                transfer_result = asset_transfer(
                    self.exchange,
                    {
                        "type": TransferType.MAIN_TO_USDT_FUTURE.value,
                        "asset": "USDT",
                        "amount": str(usdt_balance["free"]),
                    },
                )
                self.logger.info(f"资产归集结果: {transfer_result}")
                send_message(
                    f"【任务成功】资产归集\n账号信息: {self.exchange_key.exchange}_{self.exchange_key.account}\n执行时间: {datetime.now()}\n归集金额: {usdt_balance['free']}"
                )
                time.sleep(self.api_sleep)
            else:
                self.logger.info("现货账户没有可用USDT余额，无需归集")
        except Exception as e:
            self.logger.error(f"资产归集异常: {str(e)}")
            send_message(
                f"【任务异常】资产归集\n账号信息: {self.exchange_key.exchange}_{self.exchange_key.account}\n执行时间: {datetime.now()}\n错误信息: {str(e)}"
            )

    def run(self):
        if is_crypto_blocked_transfer_time():
            self.logger.info("当前为禁止加密资金划转时间，不进行理财操作")
            return

        # 尝试获取锁，设置超时时间为10秒
        lock = self.redis.lock(self.lock_key, timeout=60)
        acquire_result = lock.acquire(blocking=False)

        if not acquire_result:
            print(
                f"无法获取锁，可能有其他进程正在处理该交易对: {self.exchange_key.exchange}_{self.exchange_key.account}"
            )
            return

        self.logger.info(
            f"开始运行账户余额机器人: {self.exchange_key.exchange}_{self.exchange_key.account}"
        )
        try:
            balance = self._get_idle_balance()

            self._should_buy_or_redeem(balance)

            self._asset_aggregation()

            self.logger.info(
                f"账户余额机器人运行完成: {self.exchange_key.exchange}_{self.exchange_key.account}"
            )
        except Exception as e:
            self.logger.error(f"账户余额机器人运行异常: {str(e)}")
            send_message(
                f"【任务异常】账户余额机器人\n账号信息: {self.exchange_key.exchange}_{self.exchange_key.account}\n执行时间: {datetime.now()}\n错误信息: {str(e)}",
            )
        finally:
            time.sleep(self.api_sleep)
            lock.release()


class CryptoBalanceController:
    """
    账户余额控制器，用来做机器人的统一管理
    """

    def __init__(self, is_debug: bool = False):
        self.robot_list: List[CryptoBalanceRobot] = []
        self.is_debug = is_debug

    def generate_robot_list(self):
        connect_db()
        exchange_keys = ExchangeKeys.objects().all()
        for exchange_key in exchange_keys:
            if exchange_key.is_debug != self.is_debug:
                continue
            self.robot_list.append(CryptoBalanceRobot(exchange_key))

    def run(self):
        scheduler = BlockingScheduler()
        for robot in self.robot_list:
            robot.set_earn_flexible_list()
            scheduler.add_job(
                robot.run,
                "interval",
                seconds=10,
                misfire_grace_time=5,
                max_instances=5,
                coalesce=True,
            )

        scheduler.start()

        # robot = self.robot_list[0]
        # robot.set_earn_flexible_list()
        # robot.run()


if __name__ == "__main__":
    controller = CryptoBalanceController()
    controller.generate_robot_list()
    controller.run()
