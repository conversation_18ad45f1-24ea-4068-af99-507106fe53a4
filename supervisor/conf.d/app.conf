[program:stock_sector_rank]
command=poetry run python tasks/stock_sector_rank.py
directory=%(here)s/../..
autostart=false
autorestart=true
startsecs=5
stopwaitsecs=10
user=%(ENV_USER)s
environment=PYTHONPATH="%(here)s/../.."
stdout_logfile=%(here)s/../../logs/supervisor/stock_sector_rank.stdout.log
stdout_logfile_maxbytes=1MB
stdout_logfile_backups=5
stderr_logfile=%(here)s/../../logs/supervisor/stock_sector_rank.stderr.log
stderr_logfile_maxbytes=1MB
stderr_logfile_backups=5


[program:stock_grid_strategy]
command=poetry run python tasks/stock_grid_strategy.py
directory=%(here)s/../..
autostart=false
autorestart=true
startsecs=5
stopwaitsecs=10
user=%(ENV_USER)s
environment=PYTHONPATH="%(here)s/../.."
stdout_logfile=%(here)s/../../logs/supervisor/stock_grid_strategy.stdout.log
stdout_logfile_maxbytes=1MB
stdout_logfile_backups=5
stderr_logfile=%(here)s/../../logs/supervisor/stock_grid_strategy.stderr.log
stderr_logfile_maxbytes=1MB
stderr_logfile_backups=5


[program:crypto_grid_strategy]
command=poetry run python tasks/crypto_grid_strategy.py
directory=%(here)s/../..
autostart=false
autorestart=true
startsecs=5
stopwaitsecs=10
user=%(ENV_USER)s
environment=PYTHONPATH="%(here)s/../.."
stdout_logfile=%(here)s/../../logs/supervisor/crypto_grid_strategy.stdout.log
stdout_logfile_maxbytes=1MB
stdout_logfile_backups=5
stderr_logfile=%(here)s/../../logs/supervisor/crypto_grid_strategy.stderr.log
stderr_logfile_maxbytes=1MB
stderr_logfile_backups=5

[program:crypto_balance_controller]
command=poetry run python tasks/crypto_balance_controller.py
directory=%(here)s/../..
autostart=false
autorestart=true
startsecs=5
stopwaitsecs=10
user=%(ENV_USER)s
environment=PYTHONPATH="%(here)s/../.."
stdout_logfile=%(here)s/../../logs/supervisor/crypto_balance_controller.stdout.log
stdout_logfile_maxbytes=1MB
stdout_logfile_backups=5
stderr_logfile=%(here)s/../../logs/supervisor/crypto_balance_controller.stderr.log
stderr_logfile_maxbytes=1MB
stderr_logfile_backups=5


[program:crypto_balance_history]
command=poetry run python tasks/crypt_balance_history.py
directory=%(here)s/../..
autostart=false
autorestart=true
startsecs=5
stopwaitsecs=10
user=%(ENV_USER)s
environment=PYTHONPATH="%(here)s/../.."
stdout_logfile=%(here)s/../../logs/supervisor/crypto_balance_history.stdout.log
stdout_logfile_maxbytes=1MB
stdout_logfile_backups=5
stderr_logfile=%(here)s/../../logs/supervisor/crypto_balance_history.stderr.log
stderr_logfile_maxbytes=1MB
stderr_logfile_backups=5

[program:crypto_supertrend_strategy]
command=poetry run python tasks/crypto_supertrend_strategy.py
directory=%(here)s/../..
autostart=false
autorestart=true
startsecs=5
stopwaitsecs=10
user=%(ENV_USER)s
environment=PYTHONPATH="%(here)s/../.."
stdout_logfile=%(here)s/../../logs/supervisor/crypto_supertrend_strategy.stdout.log
stdout_logfile_maxbytes=1MB
stdout_logfile_backups=5
stderr_logfile=%(here)s/../../logs/supervisor/crypto_supertrend_strategy.stderr.log
stderr_logfile_maxbytes=1MB
stderr_logfile_backups=5

[program:crypto_market_cap_rank]
command=poetry run python tasks/crypto_market_cap_rank.py
directory=%(here)s/../..
autostart=false
autorestart=true
startsecs=5
stopwaitsecs=10
user=%(ENV_USER)s
environment=PYTHONPATH="%(here)s/../.."
stdout_logfile=%(here)s/../../logs/supervisor/crypto_market_cap_rank.stdout.log
stdout_logfile_maxbytes=1MB
stdout_logfile_backups=5
stderr_logfile=%(here)s/../../logs/supervisor/crypto_market_cap_rank.stderr.log
stderr_logfile_maxbytes=1MB
stderr_logfile_backups=5




# 移除所有程序组，让每个任务独立管理
# 这样添加新任务时不会影响现有任务
