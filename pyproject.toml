[tool.poetry]
name = "investtask"
version = "0.1.0"
description = ""
authors = ["yangchen <<EMAIL>>"]
readme = "README.md"
package-mode = false

[tool.poetry.dependencies]
python = "^3.12"
mongoengine = "^0.29.1"
loguru = "^0.7.3"
requests = "^2.32.3"
pandas-market-calendars = "^5.1.0"
supervisor = "^4.2.5"
apscheduler = "^3.11.0"
cryptography = "^44.0.3"
redis = "^6.1.0"
ccxt = "^4.4.82"
retry = "^0.9.2"
pyarrow = "^20.0.0"
ta-lib = "^0.6.4"
akshare = "^1.17.20"


[tool.poetry.group.dev.dependencies]
black = "^25.1.0"
isort = "^6.0.1"

[tool.isort]
profile = "black"
line_length = 120
multi_line_output = 3
include_trailing_comma = false

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
