from mongoengine import (
    <PERSON><PERSON>anField,
    Document,
    EmbeddedDocument,
    EmbeddedDocumentField,
    StringField,
    IntField,
    FloatField,
    DateTimeField,
)


class GridStrategy(EmbeddedDocument):
    """
    网格交易策略
    """

    invest_asset = FloatField(required=True)  # 投资金额
    leverage = FloatField(required=True)  # 杠杆
    grid_size = FloatField(required=True)  # 网格大小
    grid_value = FloatField(required=True)  # 网格价值
    entry_price = FloatField(required=False)  # 入场价格
    target_price = FloatField(required=True)  # 目标价格
    buy_price = FloatField(required=True)  # 买入价格
    sell_price = FloatField(required=True)  # 卖出价格
    buy_amount = IntField(required=True)  # 买入数量
    sell_amount = IntField(required=True)  # 卖出数量
    is_active = BooleanField(required=True)  # 是否激活


class StockStrategyBase(Document):
    """
    股票策略集合
    """

    code = StringField(required=True, unique=True)  # 股票代码
    name = StringField(required=True)  # 股票名称
    price = FloatField(required=False)  # 股价
    pct_change = FloatField(required=False)  # 涨跌幅
    position_price = FloatField(required=True)  # 持仓价格
    position_amount = IntField(required=True)  # 持仓数量
    unrealized_pnl = FloatField(required=False)  # 未实现收益
    position_cost_value = FloatField(required=True)  # 持仓成本
    position_value = FloatField(required=True)  # 持仓市值
    grid_strategy = EmbeddedDocumentField(GridStrategy, required=False)  # 网格交易策略
    updated_at = DateTimeField(required=True)  # 更新时间

    meta = {"abstract": True}  # 设为抽象类，不会创建集合


class StockStrategy(StockStrategyBase):
    """
    当前股票策略
    """

    meta = {"collection": "stock_strategy", "strict": False}  # 忽略未知字段


class StockStrategyHistory(StockStrategyBase):
    """
    股票策略历史
    """
    code = StringField(required=True)
    created_at = DateTimeField(required=True)  # 创建时间

    meta = {
        "collection": "stock_strategy_history",
        "strict": False,
        "indexes": [{"fields": ["code", "created_at"], "unique": True}],
    }  # 忽略未知字段
