from mongoengine import Document, StringField, DateTimeField, FloatField


class CryptoEarnRewardsHistory(Document):
    """
    活期理财收益记录
    """

    account = StringField(required=True)
    exchange = StringField(required=True)
    asset = StringField(required=True)
    rewards = FloatField(required=True)
    product_id = StringField(required=True)
    type = StringField(required=True)
    created_at = DateTimeField(required=True)

    meta = {
        "collection": "crypto_earn_rewards_history",
        "timestamps": False,
        "strict": False,
    }
