from mongoengine import Document
from mongoengine.fields import (
    StringField,
    FloatField,
    DateTimeField,
)
import datetime


class StockTransaction(Document):
    code = StringField(required=True)
    name = StringField(required=True)
    price = FloatField(required=True)  # 或者用 DecimalField 精确金额
    amount = FloatField(required=True)  # 成交量
    transaction_value = FloatField(required=True)  # 成交额
    fee = FloatField(required=True, default=0.0)  # 手续费，默认 0
    pnl = FloatField(required=True)  # 盈亏
    trade_time = DateTimeField(required=True)  # 成交时间

    meta = {
        "collection": "stock_transactions",  # 对应集合名
        "strict": False,  # 忽略未知字段
    }
