from mongoengine import (
    Document,
    ReferenceField,
    StringField,
    IntField,
    FloatField,
    BooleanField,
    DateTimeField,
    ListField,
)
from datetime import datetime

from database.db import connect_db
from database.exchange_keys import ExchangeKeys

class CryptoSupertrend(Document):
    exchange_key = ReferenceField(ExchangeKeys)
    symbols = ListField(required=True)
    timeframe = StringField(required=True)
    period = IntField(required=True)
    multiplier = IntField(required=True)
    ratio = FloatField(required=True)
    decrease = FloatField(required=True)
    is_debug = BooleanField(required=True, default=False)
    updated_at = DateTimeField(default=datetime.now)

    meta = {
        "collection": "crypto_supertrend",
        "strict": False,
    }

if __name__ == "__main__":
    connect_db()
    all = CryptoSupertrend.objects().all()
    for item in all:
        print(item)

