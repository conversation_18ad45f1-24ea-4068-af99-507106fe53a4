from mongoengine import (
    Document,
    EmbeddedDocument,
    EmbeddedDocumentField,
    ListField,
    StringField,
    FloatField,
    DateTimeField,
)


class Position(EmbeddedDocument):
    symbol = StringField(required=True)
    side = StringField(required=True)
    # 持仓数量
    position_amount = FloatField(required=True)
    # 持仓成本
    position_price = FloatField(required=True)
    # 未实现盈亏
    unrealized_profit = FloatField(required=True)
    # 爆仓价
    liquidation_price = FloatField(required=False)
    # 标记价格
    mark_price = FloatField(required=True)
    # 持仓资产占总资产杠杆率
    leverage_rate = FloatField(required=True)
    # 保证金， 取initialMargin
    margin = FloatField(required=True)
    # 保证金率, 打到 100% 就会平仓，取marginRatio
    margin_rate = FloatField(required=True)


class AccountHistory(Document):
    account = StringField(required=True)
    exchange = StringField(required=True)
    # 全仓账户资产
    wallet_balance = FloatField(required=True)
    # 全仓未实现盈亏
    unrealized_pnl = FloatField(required=True)
    created_at = DateTimeField(required=True)
    # 总资产
    total_asset = FloatField(required=True)
    # 活期理财资产
    earn_flexible_asset = FloatField(required=True)
    updated_at = DateTimeField(required=True)
    positions = ListField(EmbeddedDocumentField(Position))
    meta = {
        "collection": "crypto_account_history",
        "timestamps": False,
        "strict": False,
    }
