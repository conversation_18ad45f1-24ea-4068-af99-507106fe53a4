from mongoengine import Document, StringField, FloatField, DateTimeField, IntField


class CryptoMarketCap(Document):
    symbol = StringField(required=True)
    name = StringField(required=True)
    price = FloatField(required=True)
    market_cap = FloatField(required=True)
    rank = IntField(required=True)
    volume = FloatField(required=True)
    volume_change_24h = FloatField(required=True)
    percent_change_1h = FloatField(required=True)
    percent_change_7d = FloatField(required=True)
    percent_change_24h = FloatField(required=True)
    updated_at = DateTimeField(required=True)

    meta = {
        "collection": "crypto_market_cap",
        "strict": False,
    }
