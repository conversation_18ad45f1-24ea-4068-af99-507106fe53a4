from mongoengine import (
    Document,
    Float<PERSON>ield,
    BooleanField,
    DateTimeField,
    IntField,
    ReferenceField,
    StringField,
)
import datetime
from database import ExchangeKeys


class CryptoGridStrategy(Document):
    exchange_key = ReferenceField(ExchangeKeys)
    invest_ratio = FloatField(required=True)
    invest_asset = FloatField(required=True)
    leverage = IntField(required=True)
    grid_size = FloatField(required=True)
    symbol = StringField(required=True)

    grid_value = FloatField(required=False)
    target_price = FloatField(required=False)
    buy_price = FloatField(required=False)
    sell_price = FloatField(required=False)
    buy_amount = FloatField(required=False)
    sell_amount = FloatField(required=False)
    price_precision = IntField(required=False)
    amount_precision = IntField(required=False)
    is_active = BooleanField(required=False)
    is_debug = BooleanField(required=False)
    updated_at = DateTimeField(default=datetime.datetime.now)

    meta = {
        "collection": "crypto_grid_strategy",
        "timestamps": False,
        "strict": False,
    }
