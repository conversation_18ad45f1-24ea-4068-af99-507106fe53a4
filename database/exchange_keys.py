from mongoengine import <PERSON>oleanField, Document, StringField, DateTimeField
import datetime


class ExchangeKeys(Document):
    account = StringField(required=True)
    exchange = StringField(required=True)
    api_key = StringField(required=True)
    api_secret = StringField(required=True)
    is_debug = BooleanField(required=True)
    created_at = DateTimeField(default=datetime.datetime.utcnow)

    meta = {
        "collection": "exchange_keys",  # 对应 Mongoose 的 model name
        "strict": False,  # 忽略未知字段
    }
