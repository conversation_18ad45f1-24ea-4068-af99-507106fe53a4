import sys
import os

# 将项目根目录添加到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from config import MONGODB_URI
from mongoengine import connect

def connect_db():
  try:
    # 直接使用 URI 中指定的数据库名称 (investFlow)
    connect(host=MONGODB_URI)
    print(f"已连接 MongoDB 数据库")
  except Exception as e:
    print(f"连接 MongoDB 数据库失败")
    print(e)

if __name__ == "__main__":
  connect_db()