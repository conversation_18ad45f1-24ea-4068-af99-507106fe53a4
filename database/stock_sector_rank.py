from mongoengine import (
    Document,
    IntField,
    StringField,
    FloatField,
    DateTimeField,
    ListField,
    EmbeddedDocument,
    EmbeddedDocumentField,
)


class StockRank(EmbeddedDocument):
    code = StringField(required=True)  # 股票代码
    name = StringField(required=True)  # 股票名称
    rank = IntField(required=True)  # 排名
    price = FloatField(required=True)  # 股价
    market_value = FloatField(required=True)  # 市值
    updated_at = DateTimeField(required=True)  # 更新时间


class SectorStocksRank(Document):
    code = StringField(required=True)  # 板块代码
    name = StringField(required=True)  # 板块名称
    rank = IntField(required=True)  # 排名
    top_stocks = ListField(EmbeddedDocumentField(StockRank))
    market_value = FloatField(required=True)  # 市值
    updated_at = DateTimeField(required=True)  # 更新时间

    meta = {
        "collection": "sector_stock_rank"
    }
