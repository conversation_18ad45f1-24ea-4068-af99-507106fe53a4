"""
网格交易相关逻辑
"""
import os
import sys

# 将项目根目录添加到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

def get_stock_amount(amount) -> int:
    return max(100, int(amount / 100) * 100)

def get_grid_data(*, invest_asset, leverage, grid_size, target_price, position_amount):
    """
    获取网格交易数据
    包括买入价、卖出价、买入数量、卖出数量、网格价值
    """
    trade_value = invest_asset * leverage * grid_size

    buy_price = (trade_value / grid_size - trade_value) / (
      (trade_value / grid_size) / target_price + position_amount
    )
    buy_amount = trade_value / buy_price

    sell_price = (trade_value / grid_size + trade_value) / (
        (trade_value / grid_size) / target_price + position_amount
    )
    sell_amount = trade_value / sell_price

    return {
      "trade_value": trade_value,
      "buy_price": buy_price,
      "buy_amount": buy_amount,
      "sell_price": sell_price,
      "sell_amount": sell_amount,
    }

if __name__ == "__main__":
    print(get_grid_data(invest_asset=50000, leverage=3, grid_size=0.08, target_price=7.4, position_amount=0))
  

