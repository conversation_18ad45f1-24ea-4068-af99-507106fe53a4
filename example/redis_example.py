#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Redis 客户端使用示例
演示如何在其他模块中使用单例模式的 RedisSentinelClient
"""

from redis_client import RedisSentinelClient


def example_usage():
    """演示 Redis 客户端的基本使用"""
    
    # 获取 Redis 客户端实例（使用默认配置）
    redis_client = RedisSentinelClient.getInstance()
    
    try:
        # 写入数据
        redis_client.write_data("user:1001", "张三", expire=3600)
        redis_client.write_data("balance:1001", "10000.50", expire=7200)
        
        # 读取数据
        username = redis_client.read_data("user:1001")
        balance = redis_client.read_data("balance:1001")
        
        print(f"用户名: {username}")
        print(f"余额: {balance}")
        
        # 验证单例模式 - 多次获取实例应该是同一个对象
        another_client = RedisSentinelClient.getInstance()
        print(f"单例验证: {redis_client is another_client}")  # True
        
        return True
        
    except Exception as e:
        print(f"Redis 操作失败: {e}")
        return False


def trading_example():
    """交易相关的 Redis 使用示例"""
    
    # 直接获取实例，无需传入任何配置
    redis = RedisSentinelClient.getInstance()
    
    try:
        # 模拟交易数据
        trade_data = {
            "symbol": "BTC/USDT",
            "price": "45000.00",
            "amount": "0.1",
            "timestamp": "2025-07-10 21:00:00"
        }
        
        # 存储交易记录
        redis.write_data("trade:latest", str(trade_data), expire=86400)
        
        # 存储账户资产
        redis.write_data("asset:total", "50000.00", expire=3600)
        
        # 读取数据
        latest_trade = redis.read_data("trade:latest")
        total_asset = redis.read_data("asset:total")
        
        print(f"最新交易: {latest_trade}")
        print(f"总资产: {total_asset}")
        
    except Exception as e:
        print(f"交易数据操作失败: {e}")


if __name__ == "__main__":
    print("=== Redis 客户端使用示例 ===")
    
    print("\n1. 基本使用示例:")
    example_usage()
    
    print("\n2. 交易数据示例:")
    trading_example()
    
    print("\n=== 示例完成 ===")
