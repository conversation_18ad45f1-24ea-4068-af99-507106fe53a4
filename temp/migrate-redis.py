import redis
import logging
from typing import Any

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def connect_redis(
    host: str, port: int, password: str = None, db: int = 0
) -> redis.Redis:
    """连接到 Redis 数据库"""
    try:
        client = redis.Redis(
            host=host,
            port=port,
            password=password,
            db=db,
            decode_responses=True,  # 自动将字节解码为字符串
        )
        # 测试连接
        client.ping()
        logger.info(f"成功连接到 Redis: {host}:{port}")
        return client
    except redis.ConnectionError as e:
        logger.error(f"连接 Redis 失败: {e}")
        raise


def migrate_redis(source_config: dict, target_config: dict, batch_size: int = 1000):
    """将数据从源 Redis 迁移到目标 Redis"""
    try:
        # 连接源和目标 Redis
        source_client = connect_redis(**source_config)
        target_client = connect_redis(**target_config)

        # 获取所有键
        keys = source_client.keys("*")
        total_keys = len(keys)
        logger.info(f"发现 {total_keys} 个键需要迁移")

        # 批量处理键
        for i in range(0, total_keys, batch_size):
            batch_keys = keys[i : i + batch_size]
            pipe = target_client.pipeline()

            for key in batch_keys:
                try:
                    # 获取键的类型
                    key_type = source_client.type(key)

                    # 根据键类型处理
                    if key_type == "string":
                        value = source_client.get(key)
                        ttl = source_client.ttl(key)
                        pipe.set(key, value)
                        if ttl > 0:
                            pipe.expire(key, ttl)

                    elif key_type == "hash":
                        value = source_client.hgetall(key)
                        ttl = source_client.ttl(key)
                        pipe.hset(key, mapping=value)
                        if ttl > 0:
                            pipe.expire(key, ttl)

                    elif key_type == "list":
                        values = source_client.lrange(key, 0, -1)
                        ttl = source_client.ttl(key)
                        pipe.rpush(key, *values)
                        if ttl > 0:
                            pipe.expire(key, ttl)

                    elif key_type == "set":
                        values = source_client.smembers(key)
                        ttl = source_client.ttl(key)
                        pipe.sadd(key, *values)
                        if ttl > 0:
                            pipe.expire(key, ttl)

                    elif key_type == "zset":
                        values = source_client.zrange(key, 0, -1, withscores=True)
                        ttl = source_client.ttl(key)
                        pipe.zadd(key, {v: s for v, s in values})
                        if ttl > 0:
                            pipe.expire(key, ttl)

                    else:
                        logger.warning(f"跳过不支持的键类型: {key_type} for key: {key}")

                except Exception as e:
                    logger.error(f"迁移键 {key} 失败: {e}")
                    continue

            # 执行批量操作
            pipe.execute()
            logger.info(f"已迁移 {min(i + batch_size, total_keys)}/{total_keys} 个键")

        logger.info("数据迁移完成")

    except Exception as e:
        logger.error(f"迁移过程出错: {e}")
        raise
    finally:
        # 关闭连接
        source_client.close()
        target_client.close()


if __name__ == "__main__":
    # 源 Redis 配置
    source_config = {
        "host": "redis-18449.crce178.ap-east-1-1.ec2.redns.redis-cloud.com",  # 替换为源 Redis 的主机地址
        "port": 18449,  # 替换为源 Redis 的端口
        "password": "5QZs4qj2vG6dJXpx9Lg2auzrsHxrwmxM",  # 替换为源 Redis 的密码（如果有）
    }

    # 目标 Redis 配置
    target_config = {
        "host": "**************",  # 替换为目标 Redis 的主机地址
        "port": 6379,  # 替换为目标 Redis 的端口
        "password": "05599123abc",  # 替换为目标 Redis 的密码（如果有）
    }

    # 执行迁移
    migrate_redis(source_config, target_config)
