import redis
from redis.sentinel import Sentinel
import logging
from typing import Any, Optional
from retry import retry

from utils.notification import send_message

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class RedisSentinelClient:
    """Redis 哨兵客户端，用于高可用读写操作（单例模式）"""

    _instance: Optional["RedisSentinelClient"] = None
    _initialized: bool = False

    # 默认配置
    DEFAULT_SENTINEL_HOSTS = [
        ("**************", 26379),
        ("*************", 26379),
        ("*************", 26380),
        ("*************", 26381),
    ]
    DEFAULT_SERVICE_NAME = "mymaster"
    DEFAULT_PASSWORD = "05599123abc"
    DEFAULT_DB = 0

    def __new__(cls, *args, **kwargs):
        """单例模式实现"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(
        self,
        sentinel_hosts: list[tuple[str, int]] = None,
        service_name: str = None,
        password: str = None,
        sentinel_password: str = None,
        db: int = None,
    ):
        """
        初始化哨兵客户端
        :param sentinel_hosts: 哨兵节点列表，例如 [('host1', 26379), ('host2', 26379)]
        :param service_name: 哨兵服务名称（主节点组名称）
        :param password: Redis 主节点密码（可选）
        :param sentinel_password: Sentinel 节点密码（可选）
        :param db: 数据库编号
        """
        # 防止重复初始化
        if self._initialized:
            return

        # 使用默认配置
        self.sentinel_hosts = sentinel_hosts or self.DEFAULT_SENTINEL_HOSTS
        self.service_name = service_name or self.DEFAULT_SERVICE_NAME
        self.password = password or self.DEFAULT_PASSWORD
        self.sentinel_password = sentinel_password or self.DEFAULT_PASSWORD
        self.db = db if db is not None else self.DEFAULT_DB

        self.sentinel = Sentinel(
            sentinels=self.sentinel_hosts,
            sentinel_kwargs={
                "password": self.sentinel_password,
                "socket_timeout": 5,
                "decode_responses": True,
            },
            socket_timeout=5,
        )
        self.master = None
        self._initialized = True

    @classmethod
    def getInstance(cls) -> "RedisSentinelClient":
        """
        获取单例实例
        :return: RedisSentinelClient 实例
        """
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    @retry(redis.RedisError, delay=1, tries=5, logger=logger)
    def get_master_client(self) -> redis.Redis:
        """获取当前主节点的 Redis 客户端，复用现有连接"""
        # 如果已有连接且可用，直接返回
        if self.master:
            try:
                # 测试连接是否有效
                self.master.ping()
                return self.master
            except (redis.RedisError, redis.ConnectionError):
                # 连接失效，需要重新建立
                send_message("主节点连接失效，正在重新连接...")
                logger.warning("主节点连接失效，正在重新连接...")
                self.master = None

        # 建立新连接
        master_addr = self.sentinel.discover_master(self.service_name)
        if not master_addr:
            send_message(f"无法找到主节点")
            raise redis.RedisError("无法找到主节点")

        self.master = self.sentinel.master_for(
            self.service_name,
            password=self.password,
            db=self.db,
            socket_timeout=5,
            decode_responses=True,
        )
        logger.info(f"连接到主节点: {master_addr}")
        return self.master

    @retry(redis.RedisError, delay=1, tries=5, logger=logger)
    def set(self, key: str, value: Any, expire: int = None):
        """写入数据到主节点"""
        client = self.get_master_client()
        client.set(key, value)
        if expire:
            client.expire(key, expire)
        logger.info(f"成功写入键: {key}")

    @retry(redis.RedisError, delay=1, tries=5, logger=logger)
    def get(self, key: str) -> Any:
        """从主节点读取数据"""
        client = self.get_master_client()
        value = client.get(key)
        logger.info(f"成功读取键: {key}")
        return value

    @retry(redis.RedisError, delay=1, tries=5, logger=logger)
    def lock(self, key: str, timeout: int = 30) -> redis.lock.Lock:
        """获取锁"""
        client = self.get_master_client()
        lock = client.lock(key, timeout=timeout)
        return lock

    @retry(redis.RedisError, delay=1, tries=5, logger=logger)
    def acquire_lock(
        self, lock: redis.lock.Lock, blocking: bool = True, blocking_timeout: int = 30
    ) -> bool:
        """获取锁"""
        return lock.acquire(blocking=blocking, blocking_timeout=blocking_timeout)

    @retry(redis.RedisError, delay=1, tries=5, logger=logger)
    def delete(self, key: str):
        """删除键"""
        client = self.get_master_client()
        client.delete(key)
        logger.info(f"成功删除键: {key}")

    def close(self):
        """关闭连接"""
        if self.master:
            self.master.close()
            logger.info("Redis 连接已关闭")


if __name__ == "__main__":
    # 示例用法 - 使用单例模式
    try:
        # 方式1：使用默认配置获取单例实例
        redis_client = RedisSentinelClient.getInstance()

        # 方式2：也可以直接实例化（会使用默认配置）
        # redis_client = RedisSentinelClient()

        # 示例：写入数据
        redis_client.set("test_key", "test_value", expire=60)

        # 示例：读取数据
        value = redis_client.get("test_key")
        logger.info(f"读取到的值: {value}")

        # 验证单例模式
        another_client = RedisSentinelClient.getInstance()
        logger.info(f"单例验证: {redis_client is another_client}")  # 应该输出 True

    except Exception as e:
        logger.error(f"操作失败: {e}")
    finally:
        redis_client.close()
