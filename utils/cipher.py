import os
import sys



# 将项目根目录添加到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

"""
 处理加解密工具
"""
import base64
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives import serialization, hashes
from database import connect_db, ExchangeKeys

def generate_key_pair():
    """
    生成密钥对
    """
    private_key = rsa.generate_private_key(public_exponent=65537, key_size=2048)
    public_key = private_key.public_key()

    # 保存私钥（用于后端解密）
    with open("./rsa_key/private_key.pem", "wb") as f:
        f.write(
            private_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption(),
            )
        )

    # 保存公钥（用于前端加密）
    with open("./rsa_key/public_key.pem", "wb") as f:
        f.write(
            public_key.public_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PublicFormat.SubjectPublicKeyInfo,
            )
        )


def decrypt_data(data):
    """
    解密数据
    
    Args:
        data: 需要解密的数据，可以是字符串、字节或Buffer
        
    Returns:
        解密后的数据
    """
    try:
        # 如果数据为空，直接返回
        if not data:
            return data
            
        # 确保数据是字节类型
        if isinstance(data, str):
            # 如果是字符串，可能是十六进制或base64编码
            try:
                # 尝试作为base64解码
                import base64
                data_bytes = base64.b64decode(data)
            except Exception:
                try:
                    # 尝试作为十六进制解码
                    data_bytes = bytes.fromhex(data)
                except Exception:
                    # 如果都不是，直接转换为字节
                    data_bytes = data.encode('utf-8')
        else:
            # 已经是字节类型
            data_bytes = data
            
        # 打印调试信息
        print(f"解密数据长度: {len(data_bytes)} 字节")
            
        try:
            with open("./rsa_key/private_key.pem", "rb") as f:
                private_key = serialization.load_pem_private_key(
                    f.read(),
                    password=None,
                )
                
                # 确保私钥是RSA类型
                if not isinstance(private_key, rsa.RSAPrivateKey):
                    raise TypeError("私钥不是RSA类型，无法进行解密操作")
                
                # 获取密钥大小（字节）
                key_size = (private_key.key_size + 7) // 8  # 位转字节，向上取整
                print(f"RSA密钥大小: {private_key.key_size} 位 ({key_size} 字节)")
                
                # 使用与前端匹配的解密参数
                decrypted_data = private_key.decrypt(
                    data_bytes,
                    padding.OAEP(
                        mgf=padding.MGF1(algorithm=hashes.SHA256()),
                        algorithm=hashes.SHA256(),
                        label=None,
                    )
                )
                
                # 返回解密后的字符串
                return decrypted_data.decode('utf-8')
        except Exception as e:
            print(f"解密过程中出错: {e}")
            # 如果解密失败，返回原始数据
            if isinstance(data, str):
                return data
            else:
                try:
                    return data.decode('utf-8')
                except:
                    return str(data)
    except Exception as e:
        # 添加错误处理
        print(f"解密失败: {e}")
        # 如果完全失败，仍然返回原始数据
        if isinstance(data, str):
            return data
        else:
            try:
                return data.decode('utf-8')
            except:
                return str(data)

if __name__ == "__main__":
    connect_db()
    api_key = ExchangeKeys.objects(exchange="binance").first().api_secret
    print(decrypt_data(api_key))
