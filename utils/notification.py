from loguru import logger
import requests
from typing import List, Dict, Optional

def send_message(
    msg: str, 
    title: str = "消息通知", 
    links: Optional[List[Dict[str, str]]] = None,
    at_users: Optional[List[str]] = None,
    webhook_url: str = "https://open.feishu.cn/open-apis/bot/v2/hook/07fc718d-2e4f-458e-9775-5a2ed3f908aa"
):
    """
    发送飞书消息通知
    
    参数:
        msg: 消息文本内容
        title: 消息标题
        links: 链接列表，每个链接是一个字典，包含 text 和 href 字段
               例如: [{"text": "请查看", "href": "http://example.com"}]
        at_users: 需要@的用户ID列表
        webhook_url: 飞书机器人的webhook地址
    
    返回:
        响应对象
    """
    # 构建内容块
    content_blocks = []
    
    # 第一个内容块
    first_block = []
    
    # 添加文本
    if msg:
        first_block.append({"tag": "text", "text": msg})
    
    # 添加链接
    if links:
        for link in links:
            first_block.append({
                "tag": "a",
                "text": link.get("text", "链接"),
                "href": link.get("href", "#")
            })
    
    # 添加@用户
    if at_users:
        for user_id in at_users:
            first_block.append({
                "tag": "at",
                "user_id": user_id
            })
    
    content_blocks.append(first_block)
    
    # 构建完整的消息体
    message = {
        "msg_type": "post",
        "content": {
            "post": {
                "zh_cn": {
                    "title": title,
                    "content": content_blocks
                }
            }
        }
    }
    
    # 发送请求
    response = requests.post(webhook_url, json=message)
    logger.info(f"飞书消息发送结果: {response}")
    return response


# 简单使用示例
def send_simple_message(msg: str, title: str = "消息通知"):
    """
    发送简单文本消息的便捷方法
    """
    return send_message(msg=msg, title=title)


# 带链接的消息示例
def send_message_with_link(*, msg: str, link_text: str, link_url: str, title: str = "消息通知"):
    """
    发送带链接的消息的便捷方法
    """
    links = [{"text": link_text, "href": link_url}]
    return send_message(msg=msg, title=title, links=links)


if __name__ == "__main__":
    # 简单消息测试
    send_simple_message(
        f"【目标价更新】position.name(position.code)\n"
        f"持仓量不足两个网格，已自动调整\n"
        f"新目标价: ¥new_target_price:.2f"
    )
    
    # 带链接的消息测试
    send_message_with_link(
        msg=f"【卖出提醒】position.name(position.code)\n"
        f"当前价: ¥price:.2f 接近卖出价: ¥sell_price:.2f\n"
        f"建议卖出: sell_amount股",
        link_text="更新交易记录",
        link_url=f"https://investflow.yc123h.fun/dashboard/stock/transactions"
    )
    
    # 完整功能测试
    send_message(
        msg="项目有更新:", 
        title="项目更新通知",
        links=[{"text": "请查看", "href": "http://www.example.com/"}],
        at_users=["ou_18eac8********17ad4f02e8bbbb"]
    )
