import sys
import os

from database.db import connect_db
from database.exchange_keys import ExchangeKeys
from utils.cipher import decrypt_data
from utils.constant import exchange_proxy

project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from retry import retry
from utils import init_logger
import ccxt
from ccxt.base.errors import BaseError
from typing import TypedDict, List, Dict, Any

logger = init_logger()


@retry(BaseError, delay=1, tries=10, logger=logger)
def cancel_all_orders(exchange: ccxt.Exchange, symbol: str):
    orders = exchange.fetch_open_orders(symbol)
    for order in orders:
        exchange.cancel_order(order["id"], symbol)


@retry(BaseError, delay=1, tries=10, logger=logger)
def get_position(exchange: ccxt.Exchange, symbol: str):
    positions = exchange.fetch_positions()
    position = [p for p in positions if p["symbol"] == symbol]
    return position, positions


@retry(BaseError, delay=1, tries=10, logger=logger)
def fetch_positions(exchange: ccxt.Exchange):
    return exchange.fetch_positions()


@retry(BaseError, delay=1, tries=10, logger=logger)
def create_order(
    exchange: ccxt.Exchange, symbol: str, side: str, size: float, price: float = None
):
    """
    创建订单
    :param exchange: 交易所实例
    :param symbol: 交易对
    :param side: 买卖方向 buy sell
    :param size: 交易数量
    :param price: 价格
    :return: 订单信息
    """
    if price:
        order = exchange.create_order(symbol, "limit", side, size, price)
        return order
    else:
        order = exchange.create_order(symbol, "market", side, size)
        return order


@retry(BaseError, delay=1, tries=10, logger=logger)
def get_order(exchange: ccxt.Exchange, symbol: str, order_id: str):
    order = exchange.fetch_order(order_id, symbol)
    return order


@retry(BaseError, delay=1, tries=10, logger=logger)
def get_ticker(exchange: ccxt.Exchange, symbol: str):
    ticker = exchange.fetch_ticker(symbol)
    return ticker


@retry(BaseError, delay=1, tries=10, logger=logger)
def get_account(exchange: ccxt.Exchange):
    """
    @deprecated
    请使用 fetch_balance
    """
    return exchange.fetch_balance()


@retry(BaseError, delay=1, tries=10, logger=logger)
def fetch_balance(exchange: ccxt.Exchange):
    return exchange.fetch_balance()


@retry(BaseError, delay=1, tries=10, logger=logger)
def set_leverage(exchange: ccxt.Exchange, leverage, symbol):
    return exchange.set_leverage(leverage, symbol)


class TotalAssetInfo(TypedDict):
    balance: Dict[str, Any]  # 或者更具体的类型
    total_asset: float
    usdt_earn_amount: float
    earn_flexible_positions: List[Dict[str, Any]]  # 或者空列表


@retry(BaseError, delay=1, tries=10, logger=logger)
def get_total_asset(exchange: ccxt.Exchange, is_debug: bool = False) -> TotalAssetInfo:
    """
    获取总资产
    :param exchange: 交易所实例
    :param is_debug: 是否处于调试模式
    :return: 总资产信息
    """
    balance = get_account(exchange)
    total_usdt = balance["total"]["USDT"]

    # 检查是否处于调试模式
    if is_debug:
        # 调试模式下不获取理财产品信息
        return {
            "balance": balance,
            "total_asset": float(total_usdt),
            "usdt_earn_amount": 0,
            "earn_flexible_positions": [],
        }

    # 非调试模式下获取理财产品信息
    earn_flexible_positions = exchange.sapi_get_simple_earn_flexible_position(
        {"size": 100}
    )["rows"]
    usdt_earn_amount = sum(
        float(p["totalAmount"]) for p in earn_flexible_positions if p["asset"] == "USDT"
    )
    return {
        "balance": balance,
        "usdt_earn_amount": usdt_earn_amount,
        "earn_flexible_positions": earn_flexible_positions,
        "total_asset": float(total_usdt) + usdt_earn_amount,
    }


@retry(BaseError, delay=1, tries=10, logger=logger)
def get_earn_flexible_list(exchange: ccxt.Exchange):
    """
    获取活期理财列表
    """
    return exchange.sapi_get_simple_earn_flexible_list({"size": 100})["rows"]


@retry(BaseError, delay=1, tries=10, logger=logger)
def get_earn_flexible_position(exchange: ccxt.Exchange):
    """
    获取活期理财持仓
    """
    return exchange.sapi_get_simple_earn_flexible_position({"size": 100})["rows"]


@retry(BaseError, delay=1, tries=10, logger=logger)
def post_earn_flexible_redeem(exchange: ccxt.Exchange, params: Dict[str, Any]):
    """
    赎回活期理财
    """
    return exchange.sapi_post_simple_earn_flexible_redeem(params)


@retry(BaseError, delay=1, tries=10, logger=logger)
def asset_transfer(exchange: ccxt.Exchange, params: Dict[str, Any]):
    """
    资金划转
    """
    return exchange.sapi_post_asset_transfer(params)


@retry(BaseError, delay=1, tries=10, logger=logger)
def post_simple_earn_flexible_subscribe(
    exchange: ccxt.Exchange, params: Dict[str, Any]
):
    """
    购买活期理财
    """
    return exchange.sapi_post_simple_earn_flexible_subscribe(params)


@retry(BaseError, delay=1, tries=10, logger=logger)
def delete_all_open_orders(exchange: ccxt.Exchange, symbol: str):
    res = exchange.fapiprivate_delete_allopenorders(
        {"symbol": symbol.replace("/", "").replace(":USDT", "")}
    )
    logger.info(f"{symbol}关闭全部订单: {res}")
    return res


@retry(BaseError, delay=1, tries=10, logger=logger)
def post_order(exchange: ccxt.Exchange, params: Dict[str, Any]):
    """
    币安下单接口
    """
    res = exchange.fapiprivate_post_order(params)
    logger.info(f"币安下单接口: {res}")
    return res


if __name__ == "__main__":
    is_debug = True
    connect_db()
    exchange_keys = [key for key in ExchangeKeys.objects().all() if is_debug]

    exchange = ccxt.binance(
        {
            "apiKey": exchange_keys[0].api_key,
            "secret": decrypt_data(exchange_keys[0].api_secret),
            "proxies": {
                "http": exchange_proxy,
                "https": exchange_proxy,
            },
            "options": {
                "defaultType": "future",  # 使用合约交易
            },
        }
    )
    exchange.set_sandbox_mode(True)

    # 测试代理是否生效
    try:
        logger.info("正在测试代理连接...")
        ip = exchange.fetch("https://api.ipify.org/")
        logger.info(f"当前 IP: {ip}")
        res = post_order(
            exchange,
            {
                "symbol": "ETCUSDT",
                "side": "SELL",
                "reduceOnly": True,
                "type": "MARKET",
                "quantity": 0.4,
            },
        )
        # res = post_order(exchange, {
        #     "symbol": "ETCUSDT",
        #     "side": "BUY",
        #     "type": "LIMIT",
        #     "price": 16,
        #     "quantity": 0.4,
        #     "timeInForce": "GTC",
        # })
        # res = post_order(exchange, {
        #     "symbol": "ETCUSDT",
        #     "side": "BUY",
        #     "type": "MARKET",
        #     "quantity": 0.4,
        # })
        logger.info(res)
    except Exception as e:
        logger.error(f"代理连接测试失败: {e}")

    # exchange.set_sandbox_mode(True)

    # print(get_total_asset(exchange, is_debug=True))
    # print(get_position(exchange, "BNB/USDT:USDT"))
    # create_order(exchange, "BTC/USDT:USDT", "buy", 1, 1000)
    # cancel_all_orders(exchange, "BNB/USDT:USDT")
