from datetime import datetime
import re
import pytz
import pandas_market_calendars as pmc
import requests


def now():
    """
    获取当前北京时间
    """
    beijing_tz = pytz.timezone("Asia/Shanghai")
    return datetime.now(beijing_tz)


def is_trade(check_time=False, date_str=None):
    """
    检查是否为交易日或交易时间

    参数:
        check_time: 是否检查具体交易时间，默认为 False 仅检查是否为交易日
        date_str: 日期字符串，格式为 'YYYY-MM-DD'，默认为今天

    返回:
        bool: 如果 check_time=False，返回是否为交易日
              如果 check_time=True，返回是否为交易时间
    """
    # 获取日期字符串
    if date_str is None:
        date_str = datetime.today().date().strftime("%Y-%m-%d")

    # 获取上交所（中国A股）的日历
    calendar = pmc.get_calendar("XSHG")
    schedule = calendar.schedule(start_date=date_str, end_date=date_str)

    # 如果不是交易日，直接返回 False
    if schedule.empty:
        return False

    # 如果只检查交易日，返回 True
    if not check_time:
        return True

    # 检查具体交易时间
    current_time = now()
    market_open = schedule.iloc[0]["market_open"]
    break_start = schedule.iloc[0]["break_start"]
    break_end = schedule.iloc[0]["break_end"]
    market_close = schedule.iloc[0]["market_close"]

    # 判断是否在交易时间内（上午段或下午段）
    is_morning_session = current_time > market_open and current_time < break_start
    is_afternoon_session = current_time > break_end and current_time < market_close

    return is_morning_session or is_afternoon_session


def to_fixed(num, digits=2):
    if digits <= 0:
        return int(num)
    return float(f"{num:.{digits}f}")


def mongodb_json_to_mongoengine(json_data, model_class):
    """
    将 MongoDB 导出的 JSON 转换为 MongoEngine 模型

    参数:
        json_data: MongoDB 导出的 JSON 数据
        model_class: MongoEngine 模型类

    返回:
        model_class 的实例
    """
    # 移除 _id 字段
    if "_id" in json_data:
        json_data.pop("_id")

    # 处理日期字段
    for key, value in list(json_data.items()):
        if isinstance(value, dict) and "$date" in value:
            json_data[key] = datetime.fromtimestamp(value["$date"] / 1000)
        elif isinstance(value, dict) and "$oid" in value:
            # 对于引用字段，可能需要额外处理
            pass
        elif (
            isinstance(value, dict)
            and key in model_class._fields
            and model_class._fields[key].document_type
        ):
            # 递归处理嵌套文档
            document_type = model_class._fields[key].document_type
            json_data[key] = mongodb_json_to_mongoengine(value, document_type)

    return model_class(**json_data)


def is_crypto_blocked_transfer_time():
    """
    检查是否为禁止加密资金划转转账时间
    """
    now = datetime.now()
    minutes = now.minute
    return (minutes >= 59 or minutes <= 1) or (minutes >= 29 and minutes <= 31)


def convert_to_ccxt_symbol(symbol):
    """
    将币安的symbol转换为ccxt的symbol
    """
    # 匹配字母和数字，插入斜杠和冒号
    match = re.match(r"([A-Z]+)(USDT)", symbol)
    if match:
        base, quote = match.groups()
        return f"{base}/{quote}:{quote}"
    return symbol


def convert_to_binance_symbol(symbol):
    # 去掉斜杠和冒号后的部分，直接拼接
    if ":USDT" in symbol:
        return symbol.split("/")[0] + "USDT"
    return symbol


def get_precision(n):
    """
    获取精度最小值, 3->0.001, 2->0.01, 1->0.1, 0->1
    """
    return 10 ** (-n) if n >= 0 else 10 ** (-n)


def get_ip():
    try:
        response = requests.get("https://api.ipify.org?format=json")
        return response.json()["ip"]
    except requests.RequestException:
        return "Unknown"


if __name__ == "__main__":
    print(get_ip())
    print(convert_to_binance_symbol("DOGE/USDT:USDT"))
    print(is_crypto_blocked_transfer_time())
