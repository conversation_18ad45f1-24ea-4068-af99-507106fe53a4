import signal
import sys
import threading
from datetime import datetime
from typing import Callable, Any, Optional


class ShutdownManager:
    """
    优雅关闭管理器
    负责处理进程终止信号和协调关闭过程，支持自定义退出时执行的方法
    
    用法示例:
    ```python
    def stop_my_service():
        # 停止服务的逻辑
        print("正在停止服务...")
        
    # 创建关闭管理器，注册退出时的回调函数
    shutdown_manager = ShutdownManager()
    shutdown_manager.register_handler(stop_my_service)
    
    # 在主循环中使用
    while not shutdown_manager.is_shutdown_requested():
        # 检查关闭超时
        if shutdown_manager.check_shutdown_timeout():
            sys.exit(1)  # 强制退出
            
        # 执行业务逻辑
        time.sleep(1)
    ```
    """
    def __init__(self, max_shutdown_time: int = 30):
        """
        初始化信号控制器
        
        参数:
            max_shutdown_time: 最大关闭时间（秒），超过此时间将强制退出
        """
        self.shutdown_in_progress = False
        self.shutdown_start_time: Optional[datetime] = None
        self.max_shutdown_time = max_shutdown_time  # 最大关闭时间（秒）
        self.shutdown_handlers: list[tuple[Callable, tuple, dict]] = []  # 退出时执行的处理函数列表
        
        # 注册信号处理函数
        signal.signal(signal.SIGTERM, self.handle_shutdown_signal)
        signal.signal(signal.SIGINT, self.handle_shutdown_signal)
    
    def register_handler(self, handler: Callable, *args, **kwargs) -> None:
        """
        注册退出时执行的处理函数
        
        参数:
            handler: 处理函数
            args: 传递给处理函数的位置参数
            kwargs: 传递给处理函数的关键字参数
        """
        self.shutdown_handlers.append((handler, args, kwargs))
        print(f"已注册退出处理函数: {handler.__name__}")
    
    def handle_shutdown_signal(self, signum: int, frame: Any) -> None:
        """
        处理关闭信号
        
        参数:
            signum: 信号编号
            frame: 当前栈帧
        """
        sig_name = "SIGTERM" if signum == signal.SIGTERM else "SIGINT"
        
        # 避免重复处理信号
        if self.shutdown_in_progress:
            print(f"已经在处理关闭过程中，忽略新的 {sig_name} 信号")
            return
            
        self.shutdown_in_progress = True
        self.shutdown_start_time = datetime.now()
        
        print(f"收到 {sig_name} 信号，开始优雅关闭进程...")
        
        # 在单独的线程中执行关闭过程，避免阻塞信号处理函数
        shutdown_thread = threading.Thread(target=self.perform_shutdown)
        shutdown_thread.daemon = False
        shutdown_thread.start()
    
    def perform_shutdown(self) -> None:
        """
        执行实际的关闭过程，调用所有注册的处理函数
        """
        try:
            # 执行所有注册的处理函数
            for handler, args, kwargs in self.shutdown_handlers:
                try:
                    print(f"执行退出处理函数: {handler.__name__}")
                    handler(*args, **kwargs)
                except Exception as e:
                    print(f"执行退出处理函数 {handler.__name__} 时发生错误: {e}")
            
        except Exception as e:
            print(f"关闭过程中发生错误: {e}")
        finally:
            # 确保 shutdown_start_time 不为 None
            if self.shutdown_start_time is not None:
                shutdown_time = (datetime.now() - self.shutdown_start_time).total_seconds()
                print(f"关闭过程完成，耗时 {shutdown_time:.2f} 秒")
            else:
                print("关闭过程完成")
    
    def is_shutdown_requested(self) -> bool:
        """
        检查是否已请求关闭
        
        返回:
            bool: 是否已请求关闭
        """
        return self.shutdown_in_progress
    
    def check_shutdown_timeout(self) -> bool:
        """
        检查关闭是否超时
        
        返回:
            bool: 是否已超时，如果为True，调用者应该执行适当的退出操作
        """
        if not self.shutdown_in_progress or not self.shutdown_start_time:
            return False
            
        elapsed = (datetime.now() - self.shutdown_start_time).total_seconds()
        if elapsed > self.max_shutdown_time:
            print(f"关闭过程超时（{elapsed:.2f}秒 > {self.max_shutdown_time}秒），强制退出")
            return True
        return False
