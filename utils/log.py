import sys
import functools
from datetime import datetime
from loguru import logger


class LoggerManager:
    def __init__(self, module_name = "logs/app_{time:YYYY-MM-DD}.log"):
        self.logger = logger.bind(module=module_name)
        self.module_name = module_name

    def get_logger(
        self,
        level="INFO",
        console_level="INFO",
        prefix=None,
    ):

        # 构建日志格式
        if prefix:
            log_format = (
                "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
                "<level>{level: <8}</level> | "
                f"<yellow>{prefix}</yellow> | "
                "<cyan>{module}</cyan>.<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
            )
        else:
            log_format = (
                "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
                "<level>{level: <8}</level> | "
                "<cyan>{module}</cyan>.<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
            )

        # 添加文件日志
        self.logger.add(
            f"{self.module_name}",
            filter=lambda r: r["extra"].get("module") == self.module_name,
            rotation="00:00",  # 每天午夜切换日志文件
            retention="14 days",
            level=level,
            format=log_format,
            enqueue=True,  # 使用队列，确保线程安全
            
        )

        # 添加控制台日志
        self.logger.add(
            sys.stdout,
            level=console_level,
            format=log_format,
            enqueue=True,
        )

        logger_adapter = RetryLoguruAdapter(self.logger)

        return logger_adapter


def init_logger(
    log_file="logs/app_{time:YYYY-MM-DD}.log",
    level="DEBUG",
    console_level="DEBUG",
    prefix=None,
):
    """
    初始化日志系统（兼容旧接口）

    参数:
        log_file: 日志文件路径，支持时间格式化
        level: 文件日志级别
        console_level: 控制台日志级别
        prefix: 日志前缀，会显示在每条日志前面

    返回:
        配置好的logger对象
    """
    return LoggerManager(module_name=log_file).get_logger(
        level=level,
        console_level=console_level,
        prefix=prefix,
    )


class RetryLoguruAdapter:
    """
    适配器类，使loguru的logger与retry库兼容

    retry库期望logger.warning(fmt, error, delay)的调用方式
    而loguru使用logger.warning(fmt % (error, delay))的格式化方式

    这个适配器会自动将retry库的调用转换为loguru兼容的格式
    """

    def __init__(self, logger):
        self.logger = logger

    def timing_decorator(
        self, description=None, log_level="INFO", threshold_seconds=None
    ):
        """
        记录函数执行时间的装饰器

        参数:
            description: 操作描述，默认使用函数名
            log_level: 日志级别，默认为 INFO
            threshold_seconds: 时间阈值（秒），如果执行时间超过此值，将提升日志级别为 WARNING

        用法:
            logger = init_logger(log_file="my_log.log")

            @logger.timing_decorator("获取数据")
            def get_data():
                # 函数内容...
        """

        def decorator(func):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                # 获取描述
                desc = description or f"{func.__name__}"

                # 记录开始时间
                start_time = datetime.now()

                # 执行函数
                result = func(*args, **kwargs)

                # 计算耗时
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()

                # 根据阈值决定日志级别
                actual_level = log_level
                if threshold_seconds and duration > threshold_seconds:
                    actual_level = "WARNING"

                # 使用当前logger实例记录
                # depth=1 表示使用调用者的位置信息
                self.logger.opt(depth=1).log(
                    actual_level, f"{desc}完成，耗时: {duration:.2f}秒"
                )

                return result

            return wrapper

        return decorator

    def warning(self, fmt, *args):
        """
        将retry库的logger.warning(fmt, error, delay)调用转换为loguru兼容的格式
        """
        # 如果fmt是格式字符串且包含%s，则进行格式化
        if isinstance(fmt, str) and "%s" in fmt:
            self.logger.opt(depth=1).warning(fmt % args)
        else:
            # 否则直接记录原始消息和参数
            self.logger.opt(depth=1).warning(f"{fmt} - {args}")

    # 转发其他日志级别的方法，并保留调用位置信息
    def debug(self, *args, **kwargs):
        return self.logger.opt(depth=1).debug(*args, **kwargs)

    def info(self, *args, **kwargs):
        return self.logger.opt(depth=1).info(*args, **kwargs)

    def error(self, *args, **kwargs):
        return self.logger.opt(depth=1).error(*args, **kwargs)

    def critical(self, *args, **kwargs):
        return self.logger.opt(depth=1).critical(*args, **kwargs)

    # 添加异常记录方法
    def exception(self, *args, **kwargs):
        return self.logger.opt(depth=1).exception(*args, **kwargs)


def timing_decorator(description=None, log_level="INFO", threshold_seconds=None):
    """
    记录函数执行时间的装饰器（兼容旧接口）

    参数:
        description: 操作描述，默认使用函数名
        log_level: 日志级别，默认为 INFO
        threshold_seconds: 时间阈值（秒），如果执行时间超过此值，将提升日志级别为 WARNING

    用法:
        @timing_decorator("获取股票数据")
        def get_stock_data():
            # 函数内容...

        @timing_decorator(threshold_seconds=5)  # 如果执行时间超过5秒，将记录为 WARNING
        def process_data():
            # 函数内容...
    """

    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 获取描述
            desc = description or f"{func.__name__}"

            # 记录开始时间
            start_time = datetime.now()

            # 执行函数
            result = func(*args, **kwargs)

            # 计算耗时
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            # 根据阈值决定日志级别
            actual_level = log_level
            if threshold_seconds and duration > threshold_seconds:
                actual_level = "WARNING"

            # 使用全局logger记录
            # depth=1 表示使用调用者的位置信息
            logger.opt(depth=1).log(actual_level, f"{desc}完成，耗时: {duration:.2f}秒")

            return result

        return wrapper

    return decorator


# 简化版装饰器，使用默认参数
def timing(func):
    """
    简化版的耗时统计装饰器

    用法:
        @timing
        def my_function():
            # 函数内容...
    """
    return timing_decorator()(func)


if __name__ == "__main__":
    logger_a = init_logger(log_file="moduleA.log", level="INFO")
    logger_b = init_logger(log_file="moduleB.log", level="INFO")

    logger_a.info("This goes to moduleA.log")
    logger_a.debug("This goes to moduleA.log")
    logger_b.info("This goes to moduleB.log")
    logger_b.debug("This goes to moduleB.log")
