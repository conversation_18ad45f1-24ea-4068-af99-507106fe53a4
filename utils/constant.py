from enum import StrEnum


exchange_proxy = "*********************************************************"

def get_orders_key(account: str, symbol: str):
    """
    获取订单key
    """
    return f"investTask:crypto:{account}:{symbol}:orders"


def get_total_asset_key(account: str):
    """
    获取总资产key
    """
    return f"investTask:crypto:{account}:total_asset"


def get_earn_flexible_key(exchange: str):
    """
    获取灵活理财key
    """
    return f"investTask:crypto:{exchange}:earn_flexible"

class TransferType(StrEnum):
    """
    币安钱包之间转账类型枚举
    """
    # 现货钱包转向U本位合约钱包
    MAIN_TO_USDT_FUTURE = "MAIN_UMFUTURE"
    # U本位合约钱包转向资金钱包
    USDT_FUTURE_TO_MAIN = "UMFUTURE_MAIN"
    # 现货钱包转向币本位合约钱包
    MAIN_TO_COIN_FUTURE = "MAIN_CMFUTURE"
    # 币本位合约钱包转向资金钱包
    COIN_FUTURE_TO_MAIN = "CMFUTURE_MAIN"
    